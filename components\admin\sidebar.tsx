"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  LayoutDashboard,
  Users,
  UserCheck,
  Target,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  Trophy,
  Globe,
  UserPlus,
  RefreshCw,
} from "lucide-react"
import { toast } from "sonner"

interface SidebarProps {
  collapsed?: boolean
  onCollapsedChange?: (collapsed: boolean) => void
}

const menuItems = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
    icon: LayoutDashboard,
    description: "Visão geral",
  },
  {
    title: "Usuários",
    href: "/admin/usuarios",
    icon: Users,
    description: "Gerenciar usuários",
  },
  {
    title: "Cambistas",
    href: "/admin/cambistas",
    icon: User<PERSON><PERSON><PERSON>,
    description: "Gestão de cambistas",
  },
  {
    title: "Afiliados",
    href: "/admin/afiliados",
    icon: UserPlus,
    description: "Gestão de afiliados",
  },
  {
    title: "Bolões",
    href: "/admin/boloes",
    icon: Target,
    description: "Controle de bolões",
  },
  {
    title: "Relatórios",
    href: "/admin/relatorios",
    icon: BarChart3,
    description: "Relatórios e analytics",
  },
  {
    title: "Sincronização",
    href: "/admin/sync",
    icon: RefreshCw,
    description: "Sincronizar dados da API",
  },
]

const bottomMenuItems = [
  {
    title: "Ver Site",
    href: "/",
    icon: Globe,
    description: "Ir para o site",
  },
]

export function AdminSidebar({ collapsed = false, onCollapsedChange }: SidebarProps) {
  const [mobileOpen, setMobileOpen] = useState(false)
  const pathname = usePathname()
  const router = useRouter()

  const handleLogout = () => {
    localStorage.removeItem("admin")
    toast.success("Logout realizado com sucesso!")
    router.push("/admin/login")
  }

  const toggleCollapsed = () => {
    onCollapsedChange?.(!collapsed)
  }

  return (
    <>
      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden bg-white shadow-md"
        onClick={() => setMobileOpen(!mobileOpen)}
      >
        {mobileOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      {/* Mobile Overlay */}
      {mobileOpen && <div className="fixed inset-0 bg-black/50 z-40 md:hidden" onClick={() => setMobileOpen(false)} />}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 z-40 h-full bg-slate-900 border-r border-slate-800 transition-all duration-300",
          collapsed ? "w-16" : "w-64",
          mobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-800">
          {!collapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">A</span>
              </div>
              <div>
                <h1 className="text-sm font-bold text-white">Admin Principal</h1>
                <p className="text-xs text-gray-400">Admin</p>
              </div>
            </div>
          )}

          {collapsed && (
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
              <span className="text-white font-bold text-sm">A</span>
            </div>
          )}

          {/* Collapse Button - Desktop only */}
          <Button
            variant="ghost"
            size="icon"
            className="hidden md:flex text-gray-400 hover:text-white"
            onClick={toggleCollapsed}
          >
            {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <Link key={item.href} href={item.href} onClick={() => setMobileOpen(false)}>
                  <div
                    className={cn(
                      "flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors group",
                      isActive ? "bg-blue-600 text-white" : "text-gray-300 hover:bg-slate-800 hover:text-white",
                    )}
                  >
                    <Icon className="h-5 w-5 flex-shrink-0" />
                    {!collapsed && (
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{item.title}</p>
                      </div>
                    )}
                    {collapsed && (
                      <div className="absolute left-16 bg-slate-800 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                        {item.title}
                      </div>
                    )}
                  </div>
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-slate-800 space-y-2">
          {/* Bottom Menu Items */}
          {bottomMenuItems.map((item) => {
            const Icon = item.icon
            return (
              <Link key={item.href} href={item.href} onClick={() => setMobileOpen(false)}>
                <div className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-300 hover:bg-slate-800 hover:text-white transition-colors group relative">
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  {!collapsed && <span className="text-sm">{item.title}</span>}
                  {collapsed && (
                    <div className="absolute left-16 bg-slate-800 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                      {item.title}
                    </div>
                  )}
                </div>
              </Link>
            )
          })}

          <Button
            variant="ghost"
            size={collapsed ? "icon" : "sm"}
            className="w-full text-gray-300 hover:text-white hover:bg-red-600/20 group relative"
            onClick={handleLogout}
          >
            <LogOut className="h-4 w-4" />
            {!collapsed && <span className="ml-2">Sair</span>}
            {collapsed && (
              <div className="absolute left-16 bg-slate-800 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                Sair
              </div>
            )}
          </Button>
        </div>
      </div>
    </>
  )
}
