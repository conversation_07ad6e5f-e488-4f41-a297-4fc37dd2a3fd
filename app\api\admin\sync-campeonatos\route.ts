import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'cbeb9f19b15e4252b3f9d3375fefcfcc'

// Competições principais para sincronizar
const MAIN_COMPETITIONS = [
  // Ligas Europeias Principais
  { code: 'PL', name: 'Premier League', area: 'England', flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿' },
  { code: 'PD', name: 'La Liga', area: 'Spain', flag: '🇪🇸' },
  { code: 'SA', name: 'Serie A', area: 'Italy', flag: '🇮🇹' },
  { code: 'BL1', name: 'Bundesliga', area: 'Germany', flag: '🇩🇪' },
  { code: 'FL1', name: 'Ligue 1', area: 'France', flag: '🇫🇷' },
  
  // Competições Europeias
  { code: 'CL', name: 'Champions League', area: 'Europe', flag: '🏆' },
  { code: 'EL', name: 'Europa League', area: 'Europe', flag: '🥈' },
  { code: 'EC', name: 'Conference League', area: 'Europe', flag: '🥉' },
  
  // Outras Ligas Europeias
  { code: 'DED', name: 'Eredivisie', area: 'Netherlands', flag: '🇳🇱' },
  { code: 'PPL', name: 'Primeira Liga', area: 'Portugal', flag: '🇵🇹' },
  { code: 'BSA', name: 'Jupiler Pro League', area: 'Belgium', flag: '🇧🇪' },
  
  // Ligas Sul-Americanas
  { code: 'BSB', name: 'Brasileirão Série A', area: 'Brazil', flag: '🇧🇷' },
  
  // Outras Ligas
  { code: 'MLS', name: 'Major League Soccer', area: 'USA', flag: '🇺🇸' }
]

async function fetchFootballData(endpoint: string) {
  try {
    console.log(`🌐 Buscando: ${FOOTBALL_API_URL}${endpoint}`)

    const response = await fetch(`${FOOTBALL_API_URL}${endpoint}`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error)
    throw error
  }
}

export async function POST() {
  try {
    await initializeDatabase()
    
    console.log("🔄 Iniciando sincronização automática de campeonatos...")

    // Buscar competições da Football Data API
    const competitionsData = await fetchFootballData('/competitions')
    let competitions = competitionsData.competitions || []

    // Filtrar por competições principais
    competitions = competitions.filter((comp: any) =>
      MAIN_COMPETITIONS.some(main => main.code === comp.code)
    )

    let syncedCount = 0
    let updatedCount = 0
    let errorCount = 0

    for (const comp of competitions) {
      try {
        // Verificar se já existe
        const existing = await executeQuery(
          'SELECT id FROM campeonatos WHERE api_id = ? OR codigo = ?',
          [comp.id, comp.code]
        )

        const mainCompInfo = MAIN_COMPETITIONS.find(main => main.code === comp.code)

        if (existing.length > 0) {
          // Atualizar existente
          await executeQuery(`
            UPDATE campeonatos SET
              nome = ?,
              descricao = ?,
              pais = ?,
              codigo = ?,
              logo_url = ?,
              data_inicio = ?,
              data_fim = ?,
              temporada_atual = ?,
              status = 'ativo',
              data_atualizacao = NOW()
            WHERE id = ?
          `, [
            comp.name,
            `${comp.area?.name} - ${comp.type}`,
            comp.area?.name || 'Internacional',
            comp.code,
            comp.emblem,
            comp.currentSeason?.startDate,
            comp.currentSeason?.endDate,
            comp.currentSeason?.id,
            existing[0].id
          ])
          
          updatedCount++
          console.log(`✅ Atualizado: ${comp.name}`)
        } else {
          // Criar novo
          await executeQuery(`
            INSERT INTO campeonatos (
              nome, descricao, pais, codigo, logo_url, 
              data_inicio, data_fim, temporada_atual, 
              api_id, status, data_criacao
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'ativo', NOW())
          `, [
            comp.name,
            `${comp.area?.name} - ${comp.type}`,
            comp.area?.name || 'Internacional',
            comp.code,
            comp.emblem,
            comp.currentSeason?.startDate,
            comp.currentSeason?.endDate,
            comp.currentSeason?.id,
            comp.id
          ])
          
          syncedCount++
          console.log(`🆕 Criado: ${comp.name}`)
        }

        // Delay para evitar rate limit
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`❌ Erro ao sincronizar ${comp.name}:`, error)
        errorCount++
      }
    }

    console.log(`✅ Sincronização concluída: ${syncedCount} criados, ${updatedCount} atualizados, ${errorCount} erros`)

    return NextResponse.json({
      success: true,
      message: "Sincronização de campeonatos concluída",
      data: {
        synced: syncedCount,
        updated: updatedCount,
        errors: errorCount,
        total_processed: competitions.length
      }
    })

  } catch (error) {
    console.error("❌ Erro na sincronização:", error)
    return NextResponse.json({
      success: false,
      error: "Erro na sincronização de campeonatos",
      message: error.message
    }, { status: 500 })
  }
}
