// Testar se a API está retornando todos os jogos dos campeonatos selecionados

async function testCampeonatosCompletos() {
  try {
    console.log('🧪 Testando campeonatos completos...')
    
    // Testar API de bolões
    const response = await fetch('http://localhost:3000/api/boloes')
    const data = await response.json()
    
    console.log(`Status: ${response.status}`)
    
    if (response.ok && data.boloes && data.boloes.length > 0) {
      const bolao = data.boloes[0]
      
      console.log(`\n✅ Bolão encontrado: ${bolao.nome}`)
      console.log(`📊 Total de jogos: ${bolao.total_jogos}`)
      console.log(`🏆 Campeonatos selecionados: ${bolao.campeonatos_selecionados?.length || 0}`)
      
      // Mostrar campeonatos selecionados
      if (bolao.campeonatos_selecionados && bolao.campeonatos_selecionados.length > 0) {
        console.log(`\n🏆 Campeonatos no bolão:`)
        bolao.campeonatos_selecionados.forEach((campeonato, index) => {
          console.log(`  ${index + 1}. ${campeonato.nome}`)
        })
      }
      
      // Agrupar jogos por campeonato
      if (bolao.jogos && bolao.jogos.length > 0) {
        const jogosPorCampeonato = bolao.jogos.reduce((acc, jogo) => {
          const campeonato = jogo.campeonato_nome
          if (!acc[campeonato]) {
            acc[campeonato] = []
          }
          acc[campeonato].push(jogo)
          return acc
        }, {})
        
        console.log(`\n⚽ Jogos por campeonato:`)
        Object.entries(jogosPorCampeonato).forEach(([campeonato, jogos]) => {
          console.log(`\n🏆 ${campeonato}: ${jogos.length} jogos`)
          
          // Mostrar alguns jogos de exemplo
          jogos.slice(0, 5).forEach((jogo, index) => {
            const dataJogo = new Date(jogo.data_jogo).toLocaleString('pt-BR')
            console.log(`  ${index + 1}. ${jogo.time_casa_nome} vs ${jogo.time_fora_nome} - ${dataJogo}`)
          })
          
          if (jogos.length > 5) {
            console.log(`  ... e mais ${jogos.length - 5} jogos`)
          }
        })
        
        console.log(`\n📊 Resumo:`)
        console.log(`  - Total de campeonatos: ${Object.keys(jogosPorCampeonato).length}`)
        console.log(`  - Total de jogos: ${bolao.jogos.length}`)
        console.log(`  - Campeonatos: ${Object.keys(jogosPorCampeonato).join(', ')}`)
        
      } else {
        console.log(`\n❌ Nenhum jogo encontrado no bolão`)
      }
      
    } else {
      console.log(`❌ Erro: ${data.error || 'Nenhum bolão encontrado'}`)
    }

    // Testar também a estrutura na página principal
    console.log(`\n🌐 Testando como apareceria na página principal...`)
    
    if (response.ok && data.boloes && data.boloes.length > 0) {
      const bolao = data.boloes[0]
      
      if (bolao.jogos && bolao.jogos.length > 0) {
        // Simular como os jogos seriam formatados na página principal
        const jogosFormatados = bolao.jogos.map((jogo) => ({
          id: jogo.id,
          competition: jogo.campeonato_nome?.toLowerCase().replace(/\s+/g, '-') || 'campeonato',
          competitionName: jogo.campeonato_nome || 'Campeonato',
          homeTeam: {
            id: jogo.time_casa_id,
            name: jogo.time_casa_nome || "Time Casa",
            shortName: jogo.time_casa_curto || "TC",
            crest: jogo.time_casa_logo || "/placeholder.svg"
          },
          awayTeam: {
            id: jogo.time_fora_id,
            name: jogo.time_fora_nome || "Time Fora",
            shortName: jogo.time_fora_curto || "TF",
            crest: jogo.time_fora_logo || "/placeholder.svg"
          },
          utcDate: jogo.data_jogo,
          status: jogo.status === "agendado" ? "SCHEDULED" : jogo.status.toUpperCase()
        }))
        
        // Agrupar por competição (como na página principal)
        const competitionsMap = new Map()
        
        jogosFormatados.forEach((match) => {
          if (!competitionsMap.has(match.competition)) {
            competitionsMap.set(match.competition, {
              id: match.competition,
              name: match.competitionName,
              matches: []
            })
          }
          competitionsMap.get(match.competition).matches.push(match)
        })
        
        const competitions = Array.from(competitionsMap.values()).filter(comp => comp.matches.length > 0)
        
        console.log(`\n📱 Como apareceria na página principal:`)
        competitions.forEach((competition, index) => {
          console.log(`\n${index + 1}. 🏆 ${competition.name}`)
          console.log(`   📊 ${competition.matches.length} partidas`)
          
          // Mostrar algumas partidas
          competition.matches.slice(0, 3).forEach((match, matchIndex) => {
            console.log(`   ${matchIndex + 1}. ${match.homeTeam.name} vs ${match.awayTeam.name}`)
          })
          
          if (competition.matches.length > 3) {
            console.log(`   ... e mais ${competition.matches.length - 3} partidas`)
          }
        })
      }
    }

    console.log(`\n✅ Teste concluído!`)
    console.log(`\n📋 Resultado esperado na página principal:`)
    console.log(`   - Cada campeonato selecionado aparece como uma seção`)
    console.log(`   - Todas as partidas de cada campeonato são mostradas`)
    console.log(`   - Partidas organizadas por data dentro de cada campeonato`)

  } catch (error) {
    console.error('❌ Erro no teste:', error.message)
  }
}

testCampeonatosCompletos()
