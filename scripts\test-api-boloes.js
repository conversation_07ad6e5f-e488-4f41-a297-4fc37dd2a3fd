// Testar API de bolões

async function testAPIBoloes() {
  try {
    console.log('🧪 Testando API de bolões...')
    
    const response = await fetch('http://localhost:3000/api/boloes')
    const data = await response.json()
    
    console.log('📊 Resposta da API:')
    console.log('Status:', response.status)
    console.log('Success:', data.success)
    console.log('Total de bolões:', data.boloes?.length || 0)
    
    if (data.boloes && data.boloes.length > 0) {
      console.log('')
      console.log('📋 Bolões encontrados:')
      data.boloes.forEach((bolao, index) => {
        console.log(`${index + 1}. ${bolao.nome}`)
        console.log(`   ID: ${bolao.id}`)
        console.log(`   Status: ${bolao.status}`)
        console.log(`   Valor: R$ ${bolao.valor_aposta}`)
        console.log(`   Prêmio: R$ ${bolao.premio_total}`)
        console.log(`   Participantes: ${bolao.participantes}/${bolao.max_participantes}`)
        console.log(`   Total de jogos: ${bolao.total_jogos}`)
        console.log(`   Jogos carregados: ${bolao.jogos?.length || 0}`)
        console.log(`   Banner: ${bolao.banner_image || 'Nenhum'}`)
        console.log(`   Início: ${bolao.data_inicio}`)
        console.log(`   Fim: ${bolao.data_fim}`)

        if (bolao.jogos && bolao.jogos.length > 0) {
          console.log(`   Primeiros 3 jogos:`)
          bolao.jogos.slice(0, 3).forEach((jogo, i) => {
            console.log(`     ${i+1}. ${jogo.time_casa_nome} vs ${jogo.time_fora_nome}`)
          })
        }
        console.log('')
      })
    } else {
      console.log('❌ Nenhum bolão encontrado na API')
    }
    
  } catch (error) {
    console.error('❌ Erro ao testar API:', error.message)
  }
}

testAPIBoloes()
