// Testar webhook com dados reais dos bilhetes

async function testRealWebhook() {
  try {
    console.log('🧪 Testando webhook com dados reais dos bilhetes...')
    
    // Dados dos bilhetes reais do banco
    const bilhetes = [
      {
        id: 106,
        codigo: 'BLT17526411592332151YS3W',
        transaction_id: 'pixi_01k08rvf1wfk9bg8z3t0zq1at9',
        valor: 0.60
      },
      {
        id: 107,
        codigo: 'BLT175264119146122Z6GZDN',
        transaction_id: 'pixi_01k08rwejvf46s72a6bjk246fp',
        valor: 0.60
      }
    ]

    for (const bilhete of bilhetes) {
      console.log(`\n💰 Testando pagamento do bilhete ${bilhete.codigo}...`)
      
      // Teste 1: Webhook com transaction_id
      console.log('📋 Teste 1: Webhook com transaction_id')
      const webhookData1 = {
        order_id: bilhete.transaction_id,
        transaction_id: bilhete.transaction_id,
        status: 'PAID',
        type: 'payment',
        message: 'Pagamento aprovado',
        amount: bilhete.valor,
        payment_method: 'pix'
      }

      try {
        const response1 = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(webhookData1)
        })

        const result1 = await response1.json()
        
        console.log('   Status:', response1.status)
        console.log('   Resposta:', result1.message || result1.error || 'N/A')
        
      } catch (error1) {
        console.error('   ❌ Erro no teste 1:', error1.message)
      }

      // Aguardar um pouco
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Teste 2: Webhook com código do bilhete
      console.log('📋 Teste 2: Webhook com código do bilhete')
      const webhookData2 = {
        order_id: bilhete.codigo,
        transaction_id: bilhete.transaction_id,
        status: 'PAID',
        type: 'payment',
        message: 'Pagamento aprovado',
        amount: bilhete.valor,
        payment_method: 'pix'
      }

      try {
        const response2 = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(webhookData2)
        })

        const result2 = await response2.json()
        
        console.log('   Status:', response2.status)
        console.log('   Resposta:', result2.message || result2.error || 'N/A')
        
      } catch (error2) {
        console.error('   ❌ Erro no teste 2:', error2.message)
      }

      // Aguardar um pouco
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    // Verificar status final dos bilhetes
    console.log('\n📊 Verificando status final dos bilhetes...')
    
    try {
      const statusResponse = await fetch('http://localhost:3000/api/webhook/status')
      const statusData = await statusResponse.json()
      
      console.log('Status dos bilhetes:')
      if (statusData.bilhetes_recentes) {
        statusData.bilhetes_recentes.forEach((bilhete, index) => {
          console.log(`   ${index + 1}. ${bilhete.codigo} - Status: ${bilhete.status} - Valor: R$ ${bilhete.valor}`)
        })
      }
      
    } catch (statusError) {
      console.error('❌ Erro ao verificar status:', statusError.message)
    }

    console.log('\n✅ Teste de webhook com dados reais concluído!')
    
  } catch (error) {
    console.error('❌ Erro geral no teste:', error.message)
  }
}

testRealWebhook()
