"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  RefreshCw, 
  Database, 
  Globe, 
  Trophy, 
  Users, 
  Calendar,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react"
import { toast } from "sonner"

interface SyncResult {
  success: boolean
  message: string
  data?: any
  error?: string
}

export default function SyncPage() {
  const [loading, setLoading] = useState<string | null>(null)
  const [results, setResults] = useState<Record<string, SyncResult>>({})

  const syncActions = [
    {
      id: 'sync-all',
      title: 'SINCRONIZAÇÃO COMPLETA',
      description: 'Sincroniza TODOS os campeonatos, times e partidas disponíveis',
      icon: RefreshCw,
      color: 'bg-gradient-to-r from-purple-600 to-blue-600',
      endpoint: '/api/admin/sync-all',
      priority: true
    },
    {
      id: 'clean-duplicates',
      title: 'Limpar Duplicados',
      description: 'Remove campeonatos duplicados do banco de dados',
      icon: Database,
      color: 'bg-red-500',
      endpoint: '/api/admin/clean-duplicates'
    },

    {
      id: 'generate-matches',
      title: 'Gerar Partidas',
      description: 'Cria partidas futuras para as competições',
      icon: Calendar,
      color: 'bg-green-500',
      endpoint: '/api/admin/generate-matches'
    },
    {
      id: 'sync-real-api',
      title: 'Sincronizar API Real',
      description: 'Conecta com football-data.org (requer token)',
      icon: Trophy,
      color: 'bg-purple-500',
      endpoint: '/api/admin/sync-football-api'
    }
  ]

  const handleSync = async (action: typeof syncActions[0]) => {
    setLoading(action.id)
    
    try {
      const response = await fetch(action.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type: 'all' })
      })

      const result = await response.json()
      
      setResults(prev => ({
        ...prev,
        [action.id]: result
      }))

      if (result.success) {
        toast.success(result.message)
      } else {
        toast.error(result.message || result.error)
      }

    } catch (error) {
      const errorResult = {
        success: false,
        error: 'Erro de conexão',
        message: (error as Error).message
      }
      
      setResults(prev => ({
        ...prev,
        [action.id]: errorResult
      }))
      
      toast.error('Erro ao executar sincronização')
    } finally {
      setLoading(null)
    }
  }

  const getStatusIcon = (actionId: string) => {
    const result = results[actionId]
    if (!result) return null
    
    if (result.success) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else {
      return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusBadge = (actionId: string) => {
    const result = results[actionId]
    if (!result) return null
    
    if (result.success) {
      return <Badge variant="default" className="bg-green-500">Sucesso</Badge>
    } else {
      return <Badge variant="destructive">Erro</Badge>
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Sincronização de Dados</h1>
        <p className="text-gray-600 mt-2">
          Gerencie a sincronização com APIs externas e dados do sistema
        </p>
      </div>

      {/* Informações da API */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Football-data.org API
          </CardTitle>
          <CardDescription>
            Para sincronização completa, configure sua chave da API em .env
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Como obter sua chave da API:</h4>
              <ol className="list-decimal list-inside space-y-1 text-blue-800">
                <li>Acesse <a href="https://www.football-data.org/client/register" target="_blank" className="underline">football-data.org/client/register</a></li>
                <li>Crie uma conta gratuita</li>
                <li>Copie sua chave da API</li>
                <li>Adicione no arquivo .env: <code className="bg-blue-100 px-1 rounded">FOOTBALL_API_TOKEN=sua_chave_aqui</code></li>
              </ol>
            </div>
            
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="font-semibold text-yellow-900 mb-2">Endpoints disponíveis:</h4>
              <ul className="space-y-1 text-yellow-800 text-sm">
                <li>• <code>/v4/competitions</code> - Lista de competições</li>
                <li>• <code>/v4/competitions/PL/matches</code> - Partidas da Premier League</li>
                <li>• <code>/v4/competitions/CL/matches</code> - Partidas da Champions League</li>
                <li>• <code>/v4/teams/86/matches</code> - Partidas do Real Madrid</li>
                <li>• <code>/v4/competitions/SA/scorers</code> - Artilheiros da Serie A</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Ações de Sincronização */}
      <div className="space-y-6">
        {/* Sincronização Completa - Destaque */}
        {syncActions.filter(action => action.priority).map((action) => {
          const Icon = action.icon
          const isLoading = loading === action.id
          const result = results[action.id]

          return (
            <Card key={action.id} className="border-2 border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50 hover:shadow-xl transition-all">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-3 rounded-lg ${action.color} shadow-lg`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-purple-900">{action.title}</h3>
                      <p className="text-sm text-purple-600 font-medium">🚀 Recomendado para sincronização completa</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(action.id)}
                    {getStatusBadge(action.id)}
                  </div>
                </CardTitle>
                <CardDescription className="text-purple-700 font-medium">{action.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button
                    onClick={() => handleSync(action)}
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-3 text-lg"
                    size="lg"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Sincronizando...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="mr-2 h-5 w-5" />
                        EXECUTAR SINCRONIZAÇÃO COMPLETA
                      </>
                    )}
                  </Button>

                  {result && (
                    <div className={`p-4 rounded-lg text-sm ${
                      result.success
                        ? 'bg-green-50 text-green-800 border border-green-200'
                        : 'bg-red-50 text-red-800 border border-red-200'
                    }`}>
                      <p className="font-medium">{result.message}</p>
                      {result.data && (
                        <pre className="mt-2 text-xs overflow-auto bg-white p-2 rounded">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      )}
                      {result.error && (
                        <p className="mt-2 text-xs opacity-75">Erro: {result.error}</p>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}

        {/* Outras Ações */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {syncActions.filter(action => !action.priority).map((action) => {
            const Icon = action.icon
            const isLoading = loading === action.id
            const result = results[action.id]

            return (
              <Card key={action.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`p-2 rounded-lg ${action.color}`}>
                        <Icon className="h-4 w-4 text-white" />
                      </div>
                      {action.title}
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(action.id)}
                      {getStatusBadge(action.id)}
                    </div>
                  </CardTitle>
                  <CardDescription>{action.description}</CardDescription>
                </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button
                    onClick={() => handleSync(action)}
                    disabled={isLoading}
                    className="w-full"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Executando...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Executar
                      </>
                    )}
                  </Button>
                  
                  {result && (
                    <div className={`p-3 rounded-lg text-sm ${
                      result.success 
                        ? 'bg-green-50 text-green-800 border border-green-200' 
                        : 'bg-red-50 text-red-800 border border-red-200'
                    }`}>
                      <p className="font-medium">{result.message}</p>
                      {result.data && (
                        <pre className="mt-2 text-xs overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      )}
                      {result.error && (
                        <p className="mt-2 text-xs opacity-75">Erro: {result.error}</p>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
        </div>
      </div>

      {/* Estatísticas */}
      <Card>
        <CardHeader>
          <CardTitle>Status do Sistema</CardTitle>
          <CardDescription>Informações atuais do banco de dados</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Trophy className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-blue-900">16</p>
              <p className="text-sm text-blue-600">Campeonatos</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <Users className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-green-900">414</p>
              <p className="text-sm text-green-600">Times</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Calendar className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-purple-900">1.021</p>
              <p className="text-sm text-purple-600">Partidas</p>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <Database className="h-8 w-8 text-orange-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-orange-900">6</p>
              <p className="text-sm text-orange-600">Bilhetes</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
