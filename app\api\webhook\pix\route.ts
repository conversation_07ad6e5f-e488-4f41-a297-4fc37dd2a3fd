import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()
    
    const body = await request.json()
    const { 
      qr_code_payment_id, 
      transaction_id, 
      order_id, 
      amount, 
      description, 
      status, 
      end_to_end_id, 
      last_updated_at, 
      error 
    } = body

    console.log("🔔 Webhook PIX meiodepagamento.com recebido:", {
      qr_code_payment_id,
      transaction_id,
      order_id,
      amount,
      status,
      end_to_end_id
    })

    // Salvar log do webhook
    try {
      await executeQuery(`
        INSERT INTO webhook_logs (
          transaction_id, order_id, amount, status, end_to_end_id, webhook_data, processed_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        transaction_id || null,
        order_id || null,
        amount || null,
        status || null,
        end_to_end_id || null,
        JSON.stringify({
          ...body,
          source_ip: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: request.headers.get('user-agent') || 'unknown',
          webhook_type: 'pix_meiodepagamento'
        })
      ])
      console.log("✅ Log do webhook PIX salvo")
    } catch (logError) {
      console.error("⚠️ Erro ao salvar log do webhook PIX:", logError.message)
    }

    // Validações
    if (!order_id && !transaction_id) {
      console.log("❌ Webhook PIX: order_id ou transaction_id obrigatório")
      return NextResponse.json({ 
        qr_code_payment_id: qr_code_payment_id || "",
        transaction_id: transaction_id || "",
        order_id: order_id || "",
        amount: amount || "",
        description: description || "",
        status: "ERROR",
        end_to_end_id: end_to_end_id || "",
        last_updated_at: last_updated_at || new Date().toISOString(),
        error: "order_id ou transaction_id é obrigatório"
      }, { status: 400 })
    }

    // Usar order_id ou transaction_id como identificador
    const paymentId = order_id || transaction_id

    // Mapear status para o banco
    let dbStatus = status
    if (status === 'PAID' || status === 'paid' || status === 'aprovado') {
      dbStatus = 'pago'
    } else if (status === 'PENDING' || status === 'pending' || status === 'pendente') {
      dbStatus = 'pendente'
    } else if (status === 'CANCELLED' || status === 'cancelled' || status === 'cancelado') {
      dbStatus = 'cancelado'
    } else if (status === 'FAILED' || status === 'failed' || status === 'falhou') {
      dbStatus = 'cancelado'
    }

    console.log("💾 Processando webhook:", { 
      paymentId, 
      statusOriginal: status, 
      statusBanco: dbStatus,
      amount: amount 
    })

    try {
      // Buscar bilhete pelo código ou valor
      let bilhete = []
      
      // Primeiro tentar buscar pelo código exato
      if (paymentId) {
        bilhete = await executeQuery(`
          SELECT * FROM bilhetes 
          WHERE codigo = ? 
          LIMIT 1
        `, [paymentId])
      }

      // Se não encontrou, buscar por valor e status pendente (mais recente)
      if (bilhete.length === 0 && amount) {
        const valorDecimal = parseFloat(amount)
        console.log("🔍 Buscando bilhete por valor:", valorDecimal)
        
        bilhete = await executeQuery(`
          SELECT * FROM bilhetes 
          WHERE valor_total = ? 
          AND status = 'pendente'
          AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
          ORDER BY created_at DESC
          LIMIT 1
        `, [valorDecimal])
      }

      if (bilhete.length > 0) {
        const bilheteEncontrado = bilhete[0]
        console.log("✅ Bilhete encontrado:", {
          id: bilheteEncontrado.id,
          codigo: bilheteEncontrado.codigo,
          valor: bilheteEncontrado.valor_total,
          status_atual: bilheteEncontrado.status
        })

        // Atualizar status do bilhete
        const updateResult = await executeQuery(`
          UPDATE bilhetes 
          SET status = ?, updated_at = NOW() 
          WHERE id = ?
        `, [dbStatus, bilheteEncontrado.id])

        console.log("✅ Bilhete atualizado:", updateResult)

        // Log de sucesso
        console.log("🎉 Pagamento PIX processado com sucesso:", {
          bilhete_codigo: bilheteEncontrado.codigo,
          status_anterior: bilheteEncontrado.status,
          status_novo: dbStatus,
          valor: bilheteEncontrado.valor_total,
          usuario: bilheteEncontrado.usuario_nome
        })

        // Resposta no formato padronizado
        return NextResponse.json({
          message: "Webhook PIX meiodepagamento.com processado com sucesso",
          timestamp: new Date().toISOString(),
          status: dbStatus, // "pago", "pendente", "cancelado"
          transaction_id: transaction_id || "",
          order_id: order_id || "",
          qr_code_payment_id: qr_code_payment_id || "",
          amount: amount || "",
          description: description || "",
          end_to_end_id: end_to_end_id || "",
          payment_status: status, // Status original da API
          bilhete_codigo: bilheteEncontrado.codigo,
          bilhete_id: bilheteEncontrado.id,
          bilhete_valor: parseFloat(bilheteEncontrado.valor_total),
          usuario_nome: bilheteEncontrado.usuario_nome,
          processed_successfully: true
        })
      } else {
        console.log("⚠️ Bilhete não encontrado para:", { paymentId, amount })
        
        // Buscar bilhetes recentes para debug
        const bilhetesRecentes = await executeQuery(`
          SELECT id, codigo, valor_total, status, created_at 
          FROM bilhetes 
          WHERE status = 'pendente'
          ORDER BY created_at DESC 
          LIMIT 5
        `)
        
        console.log("📋 Bilhetes pendentes recentes:", bilhetesRecentes)
        
        // Retorna resposta padronizada mesmo sem encontrar bilhete
        return NextResponse.json({
          message: "Webhook PIX recebido - bilhete não encontrado",
          timestamp: new Date().toISOString(),
          status: "not_found",
          transaction_id: transaction_id || "",
          order_id: order_id || "",
          qr_code_payment_id: qr_code_payment_id || "",
          amount: amount || "",
          description: description || "",
          end_to_end_id: end_to_end_id || "",
          payment_status: status,
          error: "Bilhete não encontrado",
          processed_successfully: false,
          bilhetes_pendentes: bilhetesRecentes.length
        })
      }

    } catch (dbError) {
      console.error("❌ Erro ao atualizar banco:", dbError)
      
      return NextResponse.json({
        qr_code_payment_id: qr_code_payment_id || "",
        transaction_id: transaction_id || "",
        order_id: order_id || "",
        amount: amount || "",
        description: description || "",
        status: "ERROR",
        end_to_end_id: end_to_end_id || "",
        last_updated_at: last_updated_at || new Date().toISOString(),
        error: "Database error"
      }, { status: 500 })
    }

  } catch (error) {
    console.error("❌ Erro geral no webhook:", error)
    
    return NextResponse.json({
      qr_code_payment_id: "",
      transaction_id: "",
      order_id: "",
      amount: "",
      description: "",
      status: "ERROR",
      end_to_end_id: "",
      last_updated_at: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Método GET para testar se o endpoint está funcionando
export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    // Buscar estatísticas de pagamentos
    const bilhetesStats = await executeQuery(`
      SELECT
        status,
        COUNT(*) as count,
        SUM(valor_total) as total_valor
      FROM bilhetes
      GROUP BY status
    `)

    const bilhetesRecentes = await executeQuery(`
      SELECT codigo, status, valor_total, created_at, transaction_id
      FROM bilhetes
      ORDER BY created_at DESC
      LIMIT 5
    `)

    return NextResponse.json({
      message: "Webhook PIX meiodepagamento.com endpoint está funcionando",
      timestamp: new Date().toISOString(),
      status: "active",
      endpoint: "/api/webhook/pix",
      method: "POST",
      estatisticas: bilhetesStats.reduce((acc, item) => {
        acc[item.status] = {
          count: item.count,
          total_valor: parseFloat(item.total_valor || 0)
        }
        return acc
      }, {}),
      bilhetes_recentes: bilhetesRecentes.map(b => ({
        codigo: b.codigo,
        status: b.status,
        valor: parseFloat(b.valor_total),
        transaction_id: b.transaction_id,
        data: b.created_at
      })),
      expected_payload: {
        qr_code_payment_id: "string",
        transaction_id: "string",
        order_id: "string",
        amount: "string",
        description: "string",
        status: "PAID|PENDING|CANCELLED|FAILED",
        end_to_end_id: "string",
        last_updated_at: "string",
        error: "string"
      }
    })
  } catch (error) {
    return NextResponse.json({
      message: "Webhook PIX meiodepagamento.com endpoint está funcionando",
      timestamp: new Date().toISOString(),
      status: "active",
      error: "Erro ao buscar estatísticas do banco"
    })
  }
}
