// Script para testar webhooks

async function testWebhooks() {
  try {
    console.log('🧪 Testando webhooks do sistema...')
    
    // 1. Testar webhook do Mercado Pago
    console.log('\n1. 🔔 Testando webhook Mercado Pago...')
    
    const mpWebhookData = {
      order_id: 'TEST_ORDER_123',
      status: 'PAID',
      type: 'payment',
      message: 'Pagamento aprovado via teste',
      transaction_id: 'MP_TEST_123',
      amount: 25.00,
      payment_method: 'pix'
    }

    try {
      const mpResponse = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mpWebhookData)
      })

      const mpResult = await mpResponse.json()
      
      console.log('📊 Resposta webhook MP:')
      console.log('   Status:', mpResponse.status)
      console.log('   Sucesso:', mpResult.success || 'N/A')
      console.log('   Mensagem:', mpResult.message || mpResult.error || 'N/A')
      
    } catch (mpError) {
      console.error('❌ Erro no webhook MP:', mpError.message)
    }

    // 2. Testar webhook PIX
    console.log('\n2. 💰 Testando webhook PIX...')
    
    const pixWebhookData = {
      qr_code_payment_id: 'QR_PIX_TEST_123',
      transaction_id: 'PIX_TEST_123',
      order_id: 'ORDER_PIX_123',
      amount: 25.00,
      description: 'Pagamento teste PIX',
      status: 'PAID',
      end_to_end_id: 'E12345678202107152130123456789',
      last_updated_at: new Date().toISOString()
    }

    try {
      const pixResponse = await fetch('http://localhost:3000/api/webhook/pix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pixWebhookData)
      })

      const pixResult = await pixResponse.json()
      
      console.log('📊 Resposta webhook PIX:')
      console.log('   Status:', pixResponse.status)
      console.log('   Sucesso:', pixResult.success || 'N/A')
      console.log('   Mensagem:', pixResult.message || pixResult.error || 'N/A')
      
    } catch (pixError) {
      console.error('❌ Erro no webhook PIX:', pixError.message)
    }

    // 3. Testar webhook de status
    console.log('\n3. 📋 Testando webhook de status...')
    
    try {
      const statusResponse = await fetch('http://localhost:3000/api/webhook/status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const statusResult = await statusResponse.json()
      
      console.log('📊 Status dos webhooks:')
      console.log('   Status:', statusResponse.status)
      console.log('   Dados:', statusResult)
      
    } catch (statusError) {
      console.error('❌ Erro no status webhook:', statusError.message)
    }

    // 4. Verificar logs de webhook
    console.log('\n4. 📝 Verificando logs de webhook...')
    
    try {
      const logsResponse = await fetch('http://localhost:3000/api/pix/logs', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const logsResult = await logsResponse.json()
      
      console.log('📊 Logs de webhook:')
      console.log('   Status:', logsResponse.status)
      console.log('   Total logs:', logsResult.logs?.length || 0)
      
      if (logsResult.logs && logsResult.logs.length > 0) {
        console.log('   Últimos 3 logs:')
        logsResult.logs.slice(0, 3).forEach((log, index) => {
          console.log(`     ${index + 1}. ${log.transaction_id} - ${log.status} (${log.data_criacao})`)
        })
      }
      
    } catch (logsError) {
      console.error('❌ Erro ao buscar logs:', logsError.message)
    }

    console.log('\n✅ Teste de webhooks concluído!')
    
  } catch (error) {
    console.error('❌ Erro geral no teste de webhooks:', error.message)
  }
}

testWebhooks()
