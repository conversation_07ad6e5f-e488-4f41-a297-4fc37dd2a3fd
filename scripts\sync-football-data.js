#!/usr/bin/env node

import { initializeDatabase, executeQuery, executeQuerySingle } from '../lib/database.js'
import { FootballAPI } from '../lib/football-api.js'

const footballAPI = new FootballAPI()

// Mapeamento de competições principais para 2025
const MAIN_COMPETITIONS = {
  'PL': { name: 'Premier League', country: 'Inglaterra' },
  'PD': { name: 'La Liga', country: 'Espanha' },
  'SA': { name: 'Serie A', country: 'Itália' },
  'BL1': { name: 'Bundesliga', country: 'Alemanha' },
  'FL1': { name: 'Ligue 1', country: 'França' },
  'CL': { name: 'UEFA Champions League', country: 'Europa' },
  'EL': { name: 'UEFA Europa League', country: 'Europa' },
  'ECL': { name: 'UEFA Conference League', country: 'Europa' },
  'WC': { name: 'FIFA World Cup', country: 'Mundial' },
  'EC': { name: 'UEFA European Championship', country: 'Europa' },
  'BSA': { name: 'Brasileirão Série A', country: 'Brasil' },
  'CLI': { name: 'Copa Libertadores', country: 'América do Sul' },
  'CSA': { name: 'Copa Sul-Americana', country: 'América do Sul' }
}

async function syncCompetitions() {
  try {
    console.log('🏆 Sincronizando campeonatos...')
    
    // Buscar todas as competições da API
    const competitionsData = await footballAPI.getCompetitions()
    
    if (!competitionsData.competitions) {
      throw new Error('Nenhuma competição encontrada na API')
    }

    console.log(`📋 Encontradas ${competitionsData.competitions.length} competições na API`)

    let syncedCount = 0
    
    for (const competition of competitionsData.competitions) {
      try {
        // Verificar se é uma competição principal
        const isMainCompetition = MAIN_COMPETITIONS[competition.code]
        
        // Só sincronizar competições principais ou ativas
        if (!isMainCompetition && competition.plan !== 'TIER_ONE') {
          continue
        }

        // Verificar se já existe no banco
        const existing = await executeQuerySingle(
          'SELECT id FROM campeonatos WHERE api_id = ?',
          [competition.id.toString()]
        )

        const competitionData = {
          nome: competition.name,
          descricao: `${competition.name} - ${competition.area?.name || 'Internacional'}`,
          pais: competition.area?.name || (isMainCompetition ? MAIN_COMPETITIONS[competition.code].country : 'Internacional'),
          temporada: getCurrentSeason(),
          status: 'ativo',
          data_inicio: competition.currentSeason?.startDate || null,
          data_fim: competition.currentSeason?.endDate || null,
          api_id: competition.id.toString(),
          logo_url: competition.emblem || null
        }

        if (existing) {
          // Atualizar existente
          await executeQuery(`
            UPDATE campeonatos SET 
              nome = ?, descricao = ?, pais = ?, temporada = ?, 
              data_inicio = ?, data_fim = ?, logo_url = ?
            WHERE api_id = ?
          `, [
            competitionData.nome,
            competitionData.descricao,
            competitionData.pais,
            competitionData.temporada,
            competitionData.data_inicio,
            competitionData.data_fim,
            competitionData.logo_url,
            competitionData.api_id
          ])
          console.log(`✅ Atualizado: ${competition.name}`)
        } else {
          // Inserir novo
          await executeQuery(`
            INSERT INTO campeonatos (nome, descricao, pais, temporada, status, data_inicio, data_fim, api_id, logo_url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            competitionData.nome,
            competitionData.descricao,
            competitionData.pais,
            competitionData.temporada,
            competitionData.status,
            competitionData.data_inicio,
            competitionData.data_fim,
            competitionData.api_id,
            competitionData.logo_url
          ])
          console.log(`➕ Adicionado: ${competition.name}`)
        }
        
        syncedCount++
        
        // Delay para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        console.error(`❌ Erro ao sincronizar ${competition.name}:`, error.message)
      }
    }

    console.log(`✅ Sincronizados ${syncedCount} campeonatos`)
    return syncedCount

  } catch (error) {
    console.error('❌ Erro ao sincronizar campeonatos:', error)
    throw error
  }
}

async function syncTeamsForCompetition(competitionApiId, competitionId) {
  try {
    console.log(`👥 Sincronizando times da competição ${competitionApiId}...`)
    
    const teamsData = await footballAPI.getCompetitionTeams(competitionApiId, getCurrentSeason())
    
    if (!teamsData.teams) {
      console.log(`⚠️ Nenhum time encontrado para competição ${competitionApiId}`)
      return 0
    }

    let syncedCount = 0

    for (const team of teamsData.teams) {
      try {
        // Verificar se já existe no banco
        const existing = await executeQuerySingle(
          'SELECT id FROM times WHERE api_id = ?',
          [team.id.toString()]
        )

        const teamData = {
          nome: team.name,
          nome_curto: team.shortName || team.tla || team.name.substring(0, 3).toUpperCase(),
          cidade: team.venue || null,
          estado: null,
          pais: team.area?.name || null,
          logo_url: team.crest || null,
          api_id: team.id.toString()
        }

        if (existing) {
          // Atualizar existente
          await executeQuery(`
            UPDATE times SET 
              nome = ?, nome_curto = ?, cidade = ?, pais = ?, logo_url = ?
            WHERE api_id = ?
          `, [
            teamData.nome,
            teamData.nome_curto,
            teamData.cidade,
            teamData.pais,
            teamData.logo_url,
            teamData.api_id
          ])
        } else {
          // Inserir novo
          await executeQuery(`
            INSERT INTO times (nome, nome_curto, cidade, estado, pais, logo_url, api_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `, [
            teamData.nome,
            teamData.nome_curto,
            teamData.cidade,
            teamData.estado,
            teamData.pais,
            teamData.logo_url,
            teamData.api_id
          ])
        }
        
        syncedCount++
        
      } catch (error) {
        console.error(`❌ Erro ao sincronizar time ${team.name}:`, error.message)
      }
    }

    console.log(`✅ Sincronizados ${syncedCount} times para competição ${competitionApiId}`)
    return syncedCount

  } catch (error) {
    console.error(`❌ Erro ao sincronizar times da competição ${competitionApiId}:`, error)
    return 0
  }
}

function getCurrentSeason() {
  const now = new Date()
  const year = now.getFullYear()
  
  // Se estamos no primeiro semestre, a temporada atual é do ano anterior
  if (now.getMonth() < 6) {
    return (year - 1).toString()
  }
  
  return year.toString()
}

async function syncMatches() {
  try {
    console.log('⚽ Sincronizando partidas...')
    
    // Buscar competições ativas do banco
    const competitions = await executeQuery(`
      SELECT id, api_id, nome FROM campeonatos 
      WHERE status = 'ativo' AND api_id IS NOT NULL
      ORDER BY nome
    `)

    if (competitions.length === 0) {
      console.log('⚠️ Nenhuma competição ativa encontrada')
      return 0
    }

    let totalMatches = 0

    for (const competition of competitions) {
      try {
        console.log(`🔄 Processando ${competition.nome}...`)
        
        // Buscar partidas da próxima semana
        const dateFrom = new Date()
        const dateTo = new Date()
        dateTo.setDate(dateTo.getDate() + 14) // Próximas 2 semanas

        const matchesData = await footballAPI.getCompetitionMatches(competition.api_id, {
          dateFrom: dateFrom.toISOString().split('T')[0],
          dateTo: dateTo.toISOString().split('T')[0],
          status: 'SCHEDULED'
        })

        if (!matchesData.matches || matchesData.matches.length === 0) {
          console.log(`⚠️ Nenhuma partida encontrada para ${competition.nome}`)
          continue
        }

        let matchCount = 0

        for (const match of matchesData.matches) {
          try {
            // Buscar times no banco
            const homeTeam = await executeQuerySingle(
              'SELECT id FROM times WHERE api_id = ?',
              [match.homeTeam.id.toString()]
            )
            
            const awayTeam = await executeQuerySingle(
              'SELECT id FROM times WHERE api_id = ?',
              [match.awayTeam.id.toString()]
            )

            if (!homeTeam || !awayTeam) {
              console.log(`⚠️ Times não encontrados para partida ${match.id}`)
              continue
            }

            // Verificar se já existe
            const existing = await executeQuerySingle(
              'SELECT id FROM jogos WHERE api_id = ?',
              [match.id.toString()]
            )

            const matchData = {
              campeonato_id: competition.id,
              time_casa_id: homeTeam.id,
              time_fora_id: awayTeam.id,
              data_jogo: new Date(match.utcDate),
              local_jogo: match.venue || null,
              rodada: match.matchday || null,
              status: 'agendado',
              api_id: match.id.toString()
            }

            if (existing) {
              // Atualizar existente
              await executeQuery(`
                UPDATE jogos SET 
                  data_jogo = ?, local_jogo = ?, rodada = ?, status = ?
                WHERE api_id = ?
              `, [
                matchData.data_jogo,
                matchData.local_jogo,
                matchData.rodada,
                matchData.status,
                matchData.api_id
              ])
            } else {
              // Inserir novo
              await executeQuery(`
                INSERT INTO jogos (campeonato_id, time_casa_id, time_fora_id, data_jogo, local_jogo, rodada, status, api_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                matchData.campeonato_id,
                matchData.time_casa_id,
                matchData.time_fora_id,
                matchData.data_jogo,
                matchData.local_jogo,
                matchData.rodada,
                matchData.status,
                matchData.api_id
              ])
            }
            
            matchCount++
            
          } catch (error) {
            console.error(`❌ Erro ao sincronizar partida ${match.id}:`, error.message)
          }
        }

        console.log(`✅ ${matchCount} partidas sincronizadas para ${competition.nome}`)
        totalMatches += matchCount
        
        // Delay entre competições
        await new Promise(resolve => setTimeout(resolve, 500))
        
      } catch (error) {
        console.error(`❌ Erro ao sincronizar partidas de ${competition.nome}:`, error.message)
      }
    }

    console.log(`✅ Total de ${totalMatches} partidas sincronizadas`)
    return totalMatches

  } catch (error) {
    console.error('❌ Erro ao sincronizar partidas:', error)
    throw error
  }
}

async function main() {
  try {
    console.log('🚀 Iniciando sincronização com Football-Data.org API...')
    console.log('📅 Temporada atual:', getCurrentSeason())
    
    await initializeDatabase()
    
    // 1. Sincronizar campeonatos
    const competitionsCount = await syncCompetitions()
    
    // 2. Sincronizar times para cada competição
    const competitions = await executeQuery(`
      SELECT id, api_id, nome FROM campeonatos 
      WHERE status = 'ativo' AND api_id IS NOT NULL
      LIMIT 5
    `)
    
    let totalTeams = 0
    for (const competition of competitions) {
      const teamsCount = await syncTeamsForCompetition(competition.api_id, competition.id)
      totalTeams += teamsCount
      
      // Delay entre competições
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    // 3. Sincronizar partidas
    const matchesCount = await syncMatches()
    
    console.log('\n🎉 Sincronização concluída!')
    console.log(`📊 Resumo:`)
    console.log(`   - Campeonatos: ${competitionsCount}`)
    console.log(`   - Times: ${totalTeams}`)
    console.log(`   - Partidas: ${matchesCount}`)
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Erro na sincronização:', error)
    process.exit(1)
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { syncCompetitions, syncTeamsForCompetition, syncMatches }
