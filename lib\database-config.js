// Configuração para usar APENAS MySQL
// Conforme solicitado pelo usuário - NUNCA usar SQLite

import mysql from "mysql2/promise"

let pool = null

// Configuração do banco de dados MySQL
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top',
  charset: 'utf8mb4',
  timezone: '+00:00',
  connectionLimit: 10,
  queueLimit: 0,
  // Removidas configurações inválidas que causam warnings
  // acquireTimeout, timeout e reconnect não são válidas para mysql2
}

// Função para inicializar o pool de conexões MySQL
export async function initializeDatabase() {
  try {
    if (!pool) {
      console.log('🔧 Inicializando pool MySQL para sistema-bolao-top...')
      pool = mysql.createPool(dbConfig)
      console.log("✅ Pool de conexões MySQL inicializado com sucesso")
    }

    // Testar a conexão
    const connection = await pool.getConnection()
    await connection.ping()
    connection.release()

    console.log("✅ Conexão com MySQL sistema-bolao-top estabelecida com sucesso")
    return pool
  } catch (error) {
    console.error("❌ Erro ao inicializar banco de dados MySQL:", error)
    throw error
  }
}

// Função para obter o pool de conexões
export async function getDatabase() {
  if (!pool) {
    await initializeDatabase()
  }
  return pool
}

// Função para executar queries
export async function executeQuery(query, params = []) {
  try {
    const connection = await getDatabase()
    const [results] = await connection.execute(query, params)
    return results
  } catch (error) {
    console.error("❌ Erro ao executar query:", error)
    console.error("Query:", query)
    console.error("Params:", params)
    throw error
  }
}

// Função para executar query única
export async function executeQuerySingle(query, params = []) {
  const results = await executeQuery(query, params)
  return Array.isArray(results) && results.length > 0 ? results[0] : null
}

// Função para fechar conexões
export async function closeDatabase() {
  if (pool) {
    await pool.end()
    pool = null
    console.log("✅ Pool de conexões MySQL fechado")
  }
}
