import mysql from "mysql2/promise"

async function findAllTables() {
  try {
    console.log('🔍 Procurando todas as tabelas em todos os bancos...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      charset: 'utf8mb4'
    })

    // Listar todos os bancos
    const [databases] = await connection.execute('SHOW DATABASES')
    
    for (const db of databases) {
      const dbName = db.Database
      
      // Pular bancos do sistema
      if (['information_schema', 'mysql', 'performance_schema', 'sys'].includes(dbName)) {
        continue
      }

      try {
        console.log(`\n📊 Banco: ${dbName}`)

        // Listar todas as tabelas usando information_schema
        const [tables] = await connection.execute(`
          SELECT TABLE_NAME
          FROM information_schema.tables
          WHERE table_schema = ?
        `, [dbName])
        
        if (tables.length > 0) {
          console.log('   Tabelas encontradas:')
          
          for (const table of tables) {
            const tableName = table.TABLE_NAME
            console.log(`     - ${tableName}`)
            
            // Se a tabela contém "bilhet" ou "ticket" ou "payment", verificar conteúdo
            if (tableName.toLowerCase().includes('bilhet') || 
                tableName.toLowerCase().includes('ticket') || 
                tableName.toLowerCase().includes('payment') ||
                tableName.toLowerCase().includes('order')) {
              
              try {
                const [count] = await connection.execute(`SELECT COUNT(*) as total FROM \`${dbName}\`.\`${tableName}\``)
                console.log(`       📊 Registros: ${count[0].total}`)

                if (count[0].total > 0) {
                  // Mostrar alguns registros
                  const [records] = await connection.execute(`SELECT * FROM \`${dbName}\`.\`${tableName}\` LIMIT 3`)
                  if (records.length > 0) {
                    console.log(`       📄 Primeiros registros:`)
                    records.forEach((record, index) => {
                      console.log(`         ${index + 1}. ${JSON.stringify(record)}`)
                    })
                  }
                }
              } catch (error) {
                console.log(`       ❌ Erro ao verificar ${tableName}: ${error.message}`)
              }
            }
          }
        } else {
          console.log('   Nenhuma tabela encontrada')
        }
        
      } catch (error) {
        console.log(`   ❌ Erro ao acessar banco ${dbName}: ${error.message}`)
      }
    }

    await connection.end()

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
  }
}

findAllTables()
