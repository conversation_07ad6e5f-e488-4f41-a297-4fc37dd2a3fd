// Configuração para usar APENAS MySQL
// Conforme solicitado pelo usuário - NUNCA usar SQLite

import mysql from "mysql2/promise"

let pool: mysql.Pool | null = null

// Configuração do banco de dados MySQL com pool otimizado
const dbConfig: mysql.PoolOptions = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top',
  charset: 'utf8mb4',
  timezone: '+00:00',

  // Pool de conexões otimizado
  connectionLimit: 10, // Aumentado para permitir mais conexões simultâneas
  queueLimit: 20, // Aumentado para suportar mais requisições
  acquireTimeout: 10000, // 10 segundos para adquirir conexão
  timeout: 15000, // 15 segundos timeout para queries

  // Configurações de reconexão
  reconnect: true,
  idleTimeout: 60000, // 60 segundos para conexões inativas

  // Configurações de saúde da conexão
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,

  // Configurações adicionais para estabilidade
  maxIdle: 5, // Máximo de conexões inativas
  maxReusableConnections: 8, // Máximo de conexões reutilizáveis
}

// Função para inicializar o pool de conexões MySQL
export async function initializeDatabase(): Promise<mysql.Pool> {
  try {
    if (!pool) {
      console.log('🔧 Inicializando pool MySQL para sistema-bolao-top...')
      pool = mysql.createPool(dbConfig)
      console.log("✅ Pool de conexões MySQL inicializado com sucesso")
    }

    // Testar a conexão
    const connection = await pool.getConnection()
    await connection.ping()
    connection.release()

    console.log("✅ Conexão com MySQL sistema-bolao-top estabelecida com sucesso")
    return pool
  } catch (error) {
    console.error("❌ Erro ao inicializar banco de dados MySQL:", error)
    throw error
  }
}

// Função para obter o pool de conexões
export async function getDatabase(): Promise<mysql.Pool> {
  if (!pool) {
    await initializeDatabase()
  }
  return pool!
}

// Função para executar queries com retry automático e melhor gerenciamento de conexões
export async function executeQuery(query: string, params: any[] = [], retries: number = 2): Promise<any> {
  let connection: mysql.PoolConnection | null = null
  let lastError: any = null

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const pool = await getDatabase()

      // Usar timeout para adquirir conexão
      connection = await Promise.race([
        pool.getConnection(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout ao adquirir conexão')), 5000)
        )
      ]) as mysql.PoolConnection

      const [results] = await connection.execute(query, params)
      return results

    } catch (error) {
      lastError = error
      console.error(`❌ Erro na tentativa ${attempt}/${retries}:`, error.message)

      // Se é erro de muitas conexões, limpar pool e tentar novamente
      if (error.code === 'ER_TOO_MANY_USER_CONNECTIONS' ||
          error.message?.includes('Too many connections') ||
          error.message?.includes('Timeout ao adquirir conexão')) {

        console.warn("⚠️ Muitas conexões detectadas, limpando pool...")
        await cleanupConnections()

        if (attempt < retries) {
          console.log(`🔄 Tentando novamente em ${attempt * 2000}ms...`)
          await new Promise(resolve => setTimeout(resolve, attempt * 2000))
          continue
        }
      }

      // Para outros erros de conexão, tentar novamente
      if (error.code === 'ECONNREFUSED' || error.code === 'PROTOCOL_CONNECTION_LOST') {
        if (attempt < retries) {
          console.log(`🔄 Tentando reconectar em ${attempt * 1000}ms...`)
          await new Promise(resolve => setTimeout(resolve, attempt * 1000))
          continue
        }
      }

      // Para outros erros, não tentar novamente
      break
    } finally {
      if (connection) {
        try {
          connection.release()
        } catch (releaseError) {
          console.warn("⚠️ Erro ao liberar conexão:", releaseError.message)
        }
        connection = null
      }
    }
  }

  console.error("❌ Falha após todas as tentativas:", lastError?.message || lastError)
  throw lastError
}

// Função para limpar conexões ociosas
export async function cleanupConnections(): Promise<void> {
  try {
    if (pool) {
      // Fechar pool atual
      await pool.end()
      pool = null
      console.log("🧹 Pool de conexões limpo")

      // Aguardar um pouco antes de recriar
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Recriar pool
      await initializeDatabase()
    }
  } catch (error) {
    console.error("❌ Erro ao limpar conexões:", error.message)
  }
}

// Função para verificar a saúde do pool
export async function checkPoolHealth(): Promise<{ healthy: boolean, stats: any }> {
  try {
    const pool = await getDatabase()
    const connection = await pool.getConnection()

    // Teste simples de conectividade
    await connection.execute('SELECT 1 as test')
    connection.release()

    return {
      healthy: true,
      stats: {
        connectionLimit: dbConfig.connectionLimit,
        // Outras estatísticas podem ser adicionadas aqui
      }
    }
  } catch (error) {
    return {
      healthy: false,
      stats: { error: error.message }
    }
  }
}

// Função para executar query única
export async function executeQuerySingle(query: string, params: any[] = []): Promise<any> {
  const results = await executeQuery(query, params)
  return Array.isArray(results) && results.length > 0 ? results[0] : null
}

// Função para fechar conexões
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end()
    pool = null
    console.log("✅ Pool de conexões MySQL fechado")
  }
}
