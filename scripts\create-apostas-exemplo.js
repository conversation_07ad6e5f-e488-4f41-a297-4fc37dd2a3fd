import mysql from "mysql2/promise"

async function createApostasExemplo() {
  try {
    console.log('🎯 Criando apostas de exemplo para os bilhetes...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })

    // Buscar bilhetes existentes
    const [bilhetes] = await connection.execute(`
      SELECT id, codigo, usuario_nome FROM bilhetes ORDER BY id DESC
    `)

    console.log(`📋 Bilhetes encontrados: ${bilhetes.length}`)
    bilhetes.forEach((bilhete, index) => {
      console.log(`  ${index + 1}. ID: ${bilhete.id} - ${bilhete.codigo} - ${bilhete.usuario_nome}`)
    })

    // Buscar alguns jogos para criar apostas
    const [jogos] = await connection.execute(`
      SELECT 
        j.id,
        j.data_jogo,
        j.status,
        tc.nome as time_casa_nome,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tf.nome as time_fora_nome,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo,
        c.nome as campeonato_nome
      FROM jogos j
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      ORDER BY j.data_jogo DESC
      LIMIT 15
    `)

    console.log(`\n⚽ Jogos encontrados: ${jogos.length}`)
    jogos.slice(0, 5).forEach((jogo, index) => {
      console.log(`  ${index + 1}. ${jogo.time_casa_nome} vs ${jogo.time_fora_nome} - ${jogo.campeonato_nome}`)
    })

    // Criar apostas para cada bilhete
    for (const bilhete of bilhetes) {
      console.log(`\n💾 Criando apostas para bilhete ${bilhete.codigo}...`)
      
      // Selecionar alguns jogos aleatórios para este bilhete
      const jogosParaApostar = jogos.slice(0, Math.min(11, jogos.length)) // Máximo 11 apostas
      const resultadosPossiveis = ['casa', 'empate', 'fora']
      
      for (const jogo of jogosParaApostar) {
        // Escolher resultado aleatório
        const resultado = resultadosPossiveis[Math.floor(Math.random() * resultadosPossiveis.length)]
        
        try {
          await connection.execute(`
            INSERT INTO bilhete_apostas (bilhete_id, match_id, resultado, created_at)
            VALUES (?, ?, ?, NOW())
          `, [bilhete.id, jogo.id, resultado])
          
          console.log(`    ✅ ${jogo.time_casa_curto} vs ${jogo.time_fora_curto} - Aposta: ${resultado}`)
          
        } catch (insertError) {
          if (!insertError.message.includes('Duplicate entry')) {
            console.log(`    ❌ Erro ao inserir aposta: ${insertError.message}`)
          }
        }
      }
    }

    // Verificar apostas criadas
    console.log('\n📊 Verificando apostas criadas...')
    
    for (const bilhete of bilhetes) {
      const [apostas] = await connection.execute(`
        SELECT 
          ba.resultado,
          j.data_jogo,
          tc.nome as time_casa_nome,
          tc.nome_curto as time_casa_curto,
          tf.nome as time_fora_nome,
          tf.nome_curto as time_fora_curto,
          c.nome as campeonato_nome
        FROM bilhete_apostas ba
        JOIN jogos j ON ba.match_id = j.id
        JOIN times tc ON j.time_casa_id = tc.id
        JOIN times tf ON j.time_fora_id = tf.id
        JOIN campeonatos c ON j.campeonato_id = c.id
        WHERE ba.bilhete_id = ?
        ORDER BY j.data_jogo ASC
      `, [bilhete.id])

      console.log(`\n🎫 Bilhete ${bilhete.codigo} (${bilhete.usuario_nome}):`)
      console.log(`   📊 Total de apostas: ${apostas.length}`)
      
      if (apostas.length > 0) {
        console.log(`   🏆 Campeonatos: ${[...new Set(apostas.map(a => a.campeonato_nome))].join(', ')}`)
        console.log(`   ⚽ Primeiras apostas:`)
        apostas.slice(0, 3).forEach((aposta, index) => {
          console.log(`     ${index + 1}. ${aposta.time_casa_curto} vs ${aposta.time_fora_curto} - ${aposta.resultado}`)
        })
      }
    }

    // Testar a API
    console.log('\n🧪 Testando API de bilhetes...')
    
    if (bilhetes.length > 0) {
      const bilhete = bilhetes[0]
      
      try {
        const response = await fetch(`http://localhost:3000/api/admin/usuarios/18/bilhetes`)
        const data = await response.json()
        
        if (response.ok && data.bilhetes && data.bilhetes.length > 0) {
          const bilheteComApostas = data.bilhetes.find(b => b.id === bilhete.id)
          
          if (bilheteComApostas) {
            console.log(`   ✅ API funcionando! Bilhete ${bilheteComApostas.codigo}:`)
            console.log(`      - Apostas encontradas: ${bilheteComApostas.total_apostas}`)
            
            if (bilheteComApostas.apostas && bilheteComApostas.apostas.length > 0) {
              console.log(`      - Primeira aposta: ${bilheteComApostas.apostas[0].time_casa_nome} vs ${bilheteComApostas.apostas[0].time_fora_nome}`)
              console.log(`      - Resultado apostado: ${bilheteComApostas.apostas[0].resultado}`)
            }
          }
        }
      } catch (apiError) {
        console.log(`   ❌ Erro na API: ${apiError.message}`)
      }
    }

    await connection.end()
    
    console.log('\n🎉 Apostas de exemplo criadas com sucesso!')
    console.log('\n📋 Agora você pode:')
    console.log('   1. Acessar o painel admin → Usuários')
    console.log('   2. Clicar no botão azul (recibo) para ver bilhetes')
    console.log('   3. Ver os times apostados e resultados!')

  } catch (error) {
    console.error('❌ Erro ao criar apostas:', error.message)
  }
}

createApostasExemplo()
