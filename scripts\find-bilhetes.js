import mysql from "mysql2/promise"

async function findBilhetes() {
  try {
    console.log('🔍 Procurando bilhetes em todos os bancos...')
    
    // Configuração base sem especificar banco
    const baseConfig = {
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      charset: 'utf8mb4'
    }

    const connection = await mysql.createConnection(baseConfig)

    // Listar todos os bancos de dados
    const [databases] = await connection.execute('SHOW DATABASES')
    
    console.log('📊 Bancos de dados encontrados:')
    databases.forEach((db, index) => {
      console.log(`   ${index + 1}. ${db.Database}`)
    })

    // Procurar tabela bilhetes em cada banco
    console.log('\n🔍 Procurando tabela "bilhetes" em cada banco...')
    
    for (const db of databases) {
      const dbName = db.Database
      
      // Pular bancos do sistema
      if (['information_schema', 'mysql', 'performance_schema', 'sys'].includes(dbName)) {
        continue
      }

      try {
        await connection.execute(`USE ${dbName}`)
        
        const [tables] = await connection.execute(`
          SELECT TABLE_NAME 
          FROM information_schema.tables 
          WHERE table_schema = ? AND table_name = 'bilhetes'
        `, [dbName])

        if (tables.length > 0) {
          console.log(`\n✅ Tabela "bilhetes" encontrada no banco: ${dbName}`)
          
          // Contar registros
          const [count] = await connection.execute('SELECT COUNT(*) as total FROM bilhetes')
          console.log(`   📊 Total de registros: ${count[0].total}`)
          
          if (count[0].total > 0) {
            // Mostrar alguns registros
            const [bilhetes] = await connection.execute(`
              SELECT id, codigo, transaction_id, status, valor_total, usuario_nome
              FROM bilhetes 
              ORDER BY id DESC 
              LIMIT 5
            `)
            
            console.log('   📋 Últimos 5 bilhetes:')
            bilhetes.forEach((bilhete, index) => {
              console.log(`     ${index + 1}. ${bilhete.codigo} - ${bilhete.status} - R$ ${bilhete.valor_total} - ${bilhete.usuario_nome}`)
            })
          }
        }
      } catch (error) {
        // Ignorar erros de acesso ao banco
      }
    }

    await connection.end()

  } catch (error) {
    console.error('❌ Erro ao procurar bilhetes:', error.message)
  }
}

findBilhetes()
