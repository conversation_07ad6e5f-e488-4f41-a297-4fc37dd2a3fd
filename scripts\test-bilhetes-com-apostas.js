// Testar bilhetes com apostas criadas

async function testBilhetesComApostas() {
  try {
    console.log('🧪 Testando bilhetes com apostas criadas...')
    
    // Testar API de bilhetes do usuário
    const response = await fetch('http://localhost:3000/api/admin/usuarios/18/bilhetes')
    const data = await response.json()
    
    console.log(`Status: ${response.status}`)
    
    if (response.ok && data.bilhetes) {
      console.log(`\n✅ ${data.bilhetes.length} bilhetes encontrados`)
      
      // Mostrar estatísticas
      if (data.stats) {
        console.log(`\n📊 Estatísticas:`)
        console.log(`   - Total de bilhetes: ${data.stats.total_bilhetes}`)
        console.log(`   - Bilhetes pagos: ${data.stats.bilhetes_pagos}`)
        console.log(`   - Valor total: R$ ${data.stats.valor_total}`)
      }
      
      // Mostrar detalhes de cada bilhete
      data.bilhetes.forEach((bilhete, index) => {
        console.log(`\n🎫 Bilhete ${index + 1}: ${bilhete.codigo}`)
        console.log(`   👤 Usuário: ${bilhete.usuario_nome}`)
        console.log(`   💰 Valor: R$ ${bilhete.valor_total}`)
        console.log(`   📊 Status: ${bilhete.status}`)
        console.log(`   🎯 Total de apostas: ${bilhete.total_apostas}`)
        
        if (bilhete.apostas && bilhete.apostas.length > 0) {
          console.log(`   ⚽ Apostas realizadas:`)
          
          bilhete.apostas.forEach((aposta, apostaIndex) => {
            const resultado = aposta.resultado === 'casa' ? 'Casa' : 
                            aposta.resultado === 'empate' ? 'Empate' : 'Fora'
            
            console.log(`     ${apostaIndex + 1}. ${aposta.time_casa_nome} vs ${aposta.time_fora_nome}`)
            console.log(`        🏆 ${aposta.campeonato_nome}`)
            console.log(`        🎯 Aposta: ${resultado}`)
            console.log(`        📅 Data: ${new Date(aposta.data_jogo).toLocaleString('pt-BR')}`)
            console.log(`        📊 Status: ${aposta.jogo_status}`)
            
            if (aposta.resultado_casa !== null && aposta.resultado_fora !== null) {
              console.log(`        ⚽ Resultado: ${aposta.resultado_casa} x ${aposta.resultado_fora}`)
            }
            
            console.log(``) // Linha em branco
          })
        } else {
          console.log(`   ❌ Nenhuma aposta encontrada`)
        }
      })
      
      console.log(`\n🎉 Teste concluído com sucesso!`)
      console.log(`\n📋 Resumo:`)
      console.log(`   - ${data.bilhetes.length} bilhetes testados`)
      console.log(`   - ${data.bilhetes.reduce((sum, b) => sum + b.total_apostas, 0)} apostas totais`)
      console.log(`   - Campeonatos: ${[...new Set(data.bilhetes.flatMap(b => b.apostas?.map(a => a.campeonato_nome) || []))].join(', ')}`)
      
    } else {
      console.log(`❌ Erro: ${data.error || 'Resposta inválida'}`)
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error.message)
  }
}

testBilhetesComApostas()
