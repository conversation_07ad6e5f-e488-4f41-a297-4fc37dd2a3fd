// Criar bolão real no banco

async function createRealBolao() {
  try {
    console.log('🚀 Criando bolão real...')
    
    const dadosBolao = {
      nome: 'Bolão Brasileiro 2025',
      descricao: 'Bolão oficial com partidas do Brasileirão',
      valor_aposta: 25.00,
      premio_total: 5000.00,
      data_inicio: new Date().toISOString(),
      data_fim: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
      max_participantes: 200,
      banner_image: '/uploads/banners/banner_brasileiro.webp',
      campeonatos_selecionados: [
        { id: 2013, nome: 'Campeonato Brasileiro Série A' },
        { id: 2021, nome: 'Premier League' }
      ],
      partidas_selecionadas: []
    }
    
    console.log('📦 Dados do bolão:', {
      nome: dadosBolao.nome,
      valor_aposta: dadosBolao.valor_aposta,
      premio_total: dadosBolao.premio_total,
      campeonatos: dadosBolao.campeonatos_selecionados.length
    })
    
    const response = await fetch('http://localhost:3000/api/admin/boloes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dadosBolao)
    })
    
    const result = await response.json()
    
    console.log(`📡 Status: ${response.status}`)
    console.log(`📋 Resultado:`, result)
    
    if (response.ok) {
      console.log('✅ Bolão criado com sucesso!')
      console.log(`🆔 ID do bolão: ${result.bolao.id}`)
      
      // Verificar se aparece na listagem
      console.log('\n🔍 Verificando se aparece na listagem...')
      
      const listResponse = await fetch('http://localhost:3000/api/boloes')
      const listResult = await listResponse.json()
      
      console.log(`📊 Bolões na listagem: ${listResult.boloes.length}`)
      
      if (listResult.boloes.length > 0) {
        listResult.boloes.forEach((bolao, index) => {
          console.log(`  ${index + 1}. ${bolao.nome} (ID: ${bolao.id}) - Status: ${bolao.status}`)
        })
      }
      
    } else {
      console.log('❌ Erro na criação:')
      console.log(`   Erro: ${result.error}`)
      console.log(`   Mensagem: ${result.message}`)
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error.message)
  }
}

createRealBolao()
