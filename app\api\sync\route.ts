import { NextResponse } from "next/server"
import { syncFootballData, startAutoSync } from "@/lib/sync-football-data"

export async function POST(request: Request) {
  try {
    const { action } = await request.json()
    
    if (action === 'sync-now') {
      console.log('🔄 Iniciando sincronização manual...')
      await syncFootballData()
      
      return NextResponse.json({
        success: true,
        message: 'Sincronização executada com sucesso!'
      })
    }
    
    if (action === 'start-auto-sync') {
      console.log('⏰ Iniciando sincronização automática...')
      startAutoSync()
      
      return NextResponse.json({
        success: true,
        message: 'Sincronização automática iniciada!'
      })
    }
    
    return NextResponse.json({
      success: false,
      message: 'Ação não reconhecida'
    }, { status: 400 })
    
  } catch (error) {
    console.error('❌ Erro na API de sincronização:', error)
    
    return NextResponse.json({
      success: false,
      message: 'Erro interno do servidor',
      error: error instanceof Error ? error.message : '<PERSON>rro desconhecido'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      message: 'API de sincronização funcionando',
      endpoints: {
        'POST /api/sync': {
          'sync-now': 'Executa sincronização imediatamente',
          'start-auto-sync': 'Inicia sincronização automática de hora em hora'
        }
      }
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Erro ao verificar status da API'
    }, { status: 500 })
  }
}
