import mysql from "mysql2/promise"

async function checkBoloesTable() {
  try {
    console.log('🔍 Verificando estrutura da tabela boloes...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })

    // Verificar estrutura da tabela
    const [columns] = await connection.execute(`
      DESCRIBE boloes
    `)

    console.log(`📊 Colunas da tabela boloes:`)
    columns.forEach((col, index) => {
      console.log(`  ${index + 1}. ${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`)
    })

    // Verificar se há bolões
    const [boloes] = await connection.execute(`
      SELECT id, nome, status FROM boloes ORDER BY id DESC LIMIT 5
    `)

    console.log(`\n📋 Bolões existentes: ${boloes.length}`)
    boloes.forEach((bolao, index) => {
      console.log(`  ${index + 1}. ID: ${bolao.id} - ${bolao.nome} - Status: ${bolao.status}`)
    })

    // Testar uma atualização simples
    if (boloes.length > 0) {
      const bolaoTeste = boloes[0]
      console.log(`\n🧪 Testando atualização do bolão ID: ${bolaoTeste.id}`)
      
      try {
        await connection.execute(`
          UPDATE boloes SET status = ? WHERE id = ?
        `, [bolaoTeste.status, bolaoTeste.id])
        
        console.log(`✅ Atualização simples funcionou`)
      } catch (updateError) {
        console.log(`❌ Erro na atualização simples: ${updateError.message}`)
      }
      
      // Testar atualização completa
      console.log(`\n🧪 Testando atualização completa...`)
      
      try {
        await connection.execute(`
          UPDATE boloes SET
            nome = ?,
            descricao = ?,
            valor_aposta = ?,
            premio_total = ?,
            max_participantes = ?,
            data_inicio = ?,
            data_fim = ?
          WHERE id = ?
        `, [
          bolaoTeste.nome,
          'Teste de descrição',
          25.00,
          1000.00,
          100,
          new Date().toISOString(),
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          bolaoTeste.id
        ])
        
        console.log(`✅ Atualização completa funcionou`)
      } catch (updateError) {
        console.log(`❌ Erro na atualização completa: ${updateError.message}`)
        
        // Verificar quais colunas existem
        const colunasExistentes = columns.map(col => col.Field)
        const colunasNecessarias = [
          'nome', 'descricao', 'valor_aposta', 'valor_premium', 
          'premio_total', 'max_participantes', 'data_inicio', 'data_fim',
          'campeonatos_selecionados', 'partidas_selecionadas', 'banner_image'
        ]
        
        console.log(`\n📋 Verificando colunas necessárias:`)
        colunasNecessarias.forEach(coluna => {
          const existe = colunasExistentes.includes(coluna)
          console.log(`  ${existe ? '✅' : '❌'} ${coluna}`)
        })
        
        // Adicionar colunas que faltam
        const colunasFaltando = colunasNecessarias.filter(col => !colunasExistentes.includes(col))
        
        if (colunasFaltando.length > 0) {
          console.log(`\n🔧 Adicionando colunas que faltam:`)
          
          for (const coluna of colunasFaltando) {
            try {
              let alterQuery = ''
              
              switch (coluna) {
                case 'valor_premium':
                  alterQuery = 'ALTER TABLE boloes ADD COLUMN valor_premium DECIMAL(10,2) DEFAULT 50.00'
                  break
                case 'campeonatos_selecionados':
                  alterQuery = 'ALTER TABLE boloes ADD COLUMN campeonatos_selecionados TEXT'
                  break
                case 'partidas_selecionadas':
                  alterQuery = 'ALTER TABLE boloes ADD COLUMN partidas_selecionadas TEXT'
                  break
                case 'banner_image':
                  alterQuery = 'ALTER TABLE boloes ADD COLUMN banner_image VARCHAR(255)'
                  break
                default:
                  console.log(`  ⚠️ Não sei como adicionar a coluna: ${coluna}`)
                  continue
              }
              
              if (alterQuery) {
                await connection.execute(alterQuery)
                console.log(`  ✅ Coluna ${coluna} adicionada`)
              }
              
            } catch (alterError) {
              console.log(`  ❌ Erro ao adicionar ${coluna}: ${alterError.message}`)
            }
          }
          
          // Testar novamente após adicionar colunas
          console.log(`\n🧪 Testando atualização após adicionar colunas...`)
          
          try {
            await connection.execute(`
              UPDATE boloes SET
                nome = ?,
                descricao = ?,
                valor_aposta = ?,
                valor_premium = ?,
                premio_total = ?,
                max_participantes = ?,
                data_inicio = ?,
                data_fim = ?,
                campeonatos_selecionados = ?,
                partidas_selecionadas = ?,
                banner_image = ?
              WHERE id = ?
            `, [
              bolaoTeste.nome,
              'Teste de descrição',
              25.00,
              50.00,
              1000.00,
              100,
              new Date().toISOString(),
              new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
              JSON.stringify([]),
              JSON.stringify([]),
              null,
              bolaoTeste.id
            ])
            
            console.log(`✅ Atualização completa funcionou após correções!`)
          } catch (finalError) {
            console.log(`❌ Ainda há erro: ${finalError.message}`)
          }
        }
      }
    }

    await connection.end()
    
    console.log(`\n✅ Verificação concluída!`)

  } catch (error) {
    console.error('❌ Erro na verificação:', error.message)
  }
}

checkBoloesTable()
