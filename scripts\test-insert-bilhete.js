import mysql from "mysql2/promise"

async function testInsertBilhete() {
  try {
    console.log('🧪 Testando inserção de bilhete no banco sistema-bolao-top...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })

    // Primeiro, verificar a estrutura da tabela bilhetes
    console.log('📋 Verificando estrutura da tabela bilhetes...')
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM information_schema.columns 
      WHERE table_schema = 'sistema-bolao-top' AND table_name = 'bilhetes'
      ORDER BY ORDINAL_POSITION
    `)

    console.log('Colunas da tabela bilhetes:')
    columns.forEach((col, index) => {
      console.log(`  ${index + 1}. ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE} - Default: ${col.COLUMN_DEFAULT}`)
    })

    // Inserir um bilhete de teste
    console.log('\n💾 Inserindo bilhete de teste...')
    
    const bilheteData = {
      codigo: 'BLT17526411592332151YS3W',
      usuario_id: 21,
      usuario_nome: 'moramed',
      usuario_email: '<EMAIL>',
      usuario_cpf: '762.368.020-09',
      valor_total: 0.60,
      quantidade_apostas: 11,
      status: 'pendente',
      qr_code_pix: '00020126810014br.gov.bcb.pix2559qr-code.picpay.com...',
      transaction_id: 'pixi_01k08rvf1wfk9bg8z3t0zq1at9',
      end_to_end_id: null,
      data_expiracao: null
    }

    const [insertResult] = await connection.execute(`
      INSERT INTO bilhetes (
        codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
        valor_total, quantidade_apostas, status, qr_code_pix, transaction_id,
        end_to_end_id, data_expiracao, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      bilheteData.codigo,
      bilheteData.usuario_id,
      bilheteData.usuario_nome,
      bilheteData.usuario_email,
      bilheteData.usuario_cpf,
      bilheteData.valor_total,
      bilheteData.quantidade_apostas,
      bilheteData.status,
      bilheteData.qr_code_pix,
      bilheteData.transaction_id,
      bilheteData.end_to_end_id,
      bilheteData.data_expiracao
    ])

    console.log(`✅ Bilhete inserido com ID: ${insertResult.insertId}`)

    // Verificar se foi inserido corretamente
    const [bilheteInserido] = await connection.execute(`
      SELECT * FROM bilhetes WHERE id = ?
    `, [insertResult.insertId])

    console.log('📄 Bilhete inserido:')
    console.log(JSON.stringify(bilheteInserido[0], null, 2))

    // Testar o webhook com este bilhete
    console.log('\n🔗 Testando webhook com o bilhete inserido...')
    
    const webhookData = {
      order_id: bilheteData.transaction_id,
      transaction_id: bilheteData.transaction_id,
      status: 'PAID',
      type: 'payment',
      message: 'Pagamento aprovado',
      amount: bilheteData.valor_total,
      payment_method: 'pix'
    }

    try {
      const webhookResponse = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookData)
      })

      const webhookResult = await webhookResponse.json()
      
      console.log(`Webhook Status: ${webhookResponse.status}`)
      console.log(`Webhook Resposta: ${webhookResult.message || webhookResult.error || 'N/A'}`)
      
      // Verificar se o status foi atualizado
      const [bilheteAtualizado] = await connection.execute(`
        SELECT status FROM bilhetes WHERE id = ?
      `, [insertResult.insertId])
      
      console.log(`Status após webhook: ${bilheteAtualizado[0].status}`)
      
    } catch (webhookError) {
      console.error('❌ Erro no webhook:', webhookError.message)
    }

    // Limpar o bilhete de teste
    console.log('\n🧹 Removendo bilhete de teste...')
    await connection.execute(`DELETE FROM bilhetes WHERE id = ?`, [insertResult.insertId])
    console.log('✅ Bilhete de teste removido')

    await connection.end()

  } catch (error) {
    console.error('❌ Erro no teste:', error.message)
  }
}

testInsertBilhete()
