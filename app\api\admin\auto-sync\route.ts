import { NextResponse } from "next/server"

export const dynamic = 'force-dynamic'

export async function POST(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'all'
    const force = searchParams.get('force') === 'true'

    console.log(`🔄 Iniciando sincronização automática: ${type}`)

    const results = {
      campeonatos: null,
      partidas: null,
      times: null
    }

    // Sincronizar campeonatos
    if (type === 'all' || type === 'campeonatos') {
      try {
        console.log("🏆 Sincronizando campeonatos...")
        const campeonatosResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/admin/sync-campeonatos`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        if (campeonatosResponse.ok) {
          results.campeonatos = await campeonatosResponse.json()
          console.log("✅ Campeonatos sincronizados")
        } else {
          throw new Error(`Erro HTTP ${campeonatosResponse.status}`)
        }
      } catch (error) {
        console.error("❌ Erro ao sincronizar campeonatos:", error)
        results.campeonatos = { success: false, error: error.message }
      }
    }

    // Sincronizar times (se necessário)
    if (type === 'all' || type === 'times') {
      try {
        console.log("👥 Sincronizando times...")
        const timesResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/admin/sync-times`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        if (timesResponse.ok) {
          results.times = await timesResponse.json()
          console.log("✅ Times sincronizados")
        } else {
          console.warn("⚠️ Endpoint de times não encontrado, continuando...")
          results.times = { success: true, message: "Endpoint não implementado" }
        }
      } catch (error) {
        console.error("❌ Erro ao sincronizar times:", error)
        results.times = { success: false, error: error.message }
      }
    }

    // Sincronizar partidas
    if (type === 'all' || type === 'partidas') {
      try {
        console.log("⚽ Sincronizando partidas...")
        const partidasResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/admin/sync-partidas`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        if (partidasResponse.ok) {
          results.partidas = await partidasResponse.json()
          console.log("✅ Partidas sincronizadas")
        } else {
          throw new Error(`Erro HTTP ${partidasResponse.status}`)
        }
      } catch (error) {
        console.error("❌ Erro ao sincronizar partidas:", error)
        results.partidas = { success: false, error: error.message }
      }
    }

    // Verificar se houve algum erro
    const hasErrors = Object.values(results).some(result => 
      result && !result.success
    )

    const summary = {
      campeonatos: results.campeonatos?.data || null,
      partidas: results.partidas?.data || null,
      times: results.times?.data || null
    }

    console.log("📊 Resumo da sincronização:", summary)

    return NextResponse.json({
      success: !hasErrors,
      message: hasErrors ? "Sincronização concluída com erros" : "Sincronização concluída com sucesso",
      type,
      force,
      results,
      summary,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Erro na sincronização automática:", error)
    return NextResponse.json({
      success: false,
      error: "Erro na sincronização automática",
      message: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// Endpoint GET para verificar status
export async function GET() {
  return NextResponse.json({
    success: true,
    message: "Sistema de sincronização automática ativo",
    endpoints: {
      sync_all: "POST /api/admin/auto-sync",
      sync_campeonatos: "POST /api/admin/auto-sync?type=campeonatos",
      sync_partidas: "POST /api/admin/auto-sync?type=partidas",
      sync_times: "POST /api/admin/auto-sync?type=times"
    },
    instructions: {
      cron_daily: "0 6 * * * - Sincronização diária às 6h",
      cron_hourly: "0 * * * * - Sincronização de partidas a cada hora",
      manual: "Chame POST /api/admin/auto-sync para sincronização manual"
    },
    timestamp: new Date().toISOString()
  })
}
