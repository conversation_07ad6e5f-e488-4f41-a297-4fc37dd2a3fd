import { NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"

export async function GET() {
  try {
    console.log("🔍 Verificando saúde do banco de dados...")
    
    // Verificar saúde do pool (simplificado)
    let poolHealth = { healthy: true, stats: {} }
    
    // Tentar executar uma query simples
    let queryTest = null
    try {
      const result = await executeQuery("SELECT 1 as test")
      queryTest = {
        success: true,
        result: result[0]
      }
    } catch (queryError) {
      queryTest = {
        success: false,
        error: queryError.message
      }
    }
    
    // Verificar variáveis do MySQL relacionadas a conexões
    let connectionStats = null
    try {
      const stats = await executeQuery(`
        SHOW STATUS WHERE Variable_name IN (
          'Connections',
          'Max_used_connections', 
          'Threads_connected',
          'Threads_running',
          'Aborted_connects'
        )
      `)
      connectionStats = stats.reduce((acc: any, stat: any) => {
        acc[stat.Variable_name] = stat.Value
        return acc
      }, {})
    } catch (statsError) {
      connectionStats = { error: statsError.message }
    }
    
    const isHealthy = poolHealth.healthy && queryTest.success
    
    return NextResponse.json({
      healthy: isHealthy,
      timestamp: new Date().toISOString(),
      pool: poolHealth,
      query: queryTest,
      mysql_stats: connectionStats,
      recommendations: isHealthy ? [] : [
        "Verificar se o MySQL está rodando",
        "Verificar configurações de conexão",
        "Considerar reiniciar o pool de conexões"
      ]
    })
    
  } catch (error) {
    console.error("❌ Erro ao verificar saúde do banco:", error)
    return NextResponse.json(
      {
        healthy: false,
        error: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
