import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function checkBoloes() {
  try {
    console.log('🔍 Verificando bolões no banco de dados...')
    
    await initializeDatabase()

    // Verificar todos os bolões
    const boloes = await executeQuery(`
      SELECT 
        id, 
        nome, 
        status, 
        data_inicio, 
        data_fim, 
        valor_aposta,
        premio_total,
        max_participantes,
        data_criacao
      FROM boloes 
      ORDER BY id DESC
    `)

    console.log(`📊 Total de bolões encontrados: ${boloes.length}`)
    console.log('')

    if (boloes.length === 0) {
      console.log('❌ Nenhum bolão encontrado no banco!')
      return
    }

    boloes.forEach((bolao, index) => {
      console.log(`${index + 1}. Bolão ID: ${bolao.id}`)
      console.log(`   Nome: ${bolao.nome}`)
      console.log(`   Status: ${bolao.status}`)
      console.log(`   Valor: R$ ${bolao.valor_aposta}`)
      console.log(`   Prêmio: R$ ${bolao.premio_total}`)
      console.log(`   Início: ${bolao.data_inicio}`)
      console.log(`   Fim: ${bolao.data_fim}`)
      console.log(`   Criado: ${bolao.data_criacao}`)
      console.log('')
    })

    // Verificar jogos associados aos bolões
    console.log('🎮 Verificando jogos dos bolões...')
    
    for (const bolao of boloes) {
      const jogos = await executeQuery(`
        SELECT COUNT(*) as total_jogos
        FROM bolao_jogos bj
        JOIN jogos j ON bj.jogo_id = j.id
        WHERE bj.bolao_id = ?
      `, [bolao.id])

      console.log(`   Bolão ${bolao.id} (${bolao.nome}): ${jogos[0]?.total_jogos || 0} jogos`)
    }

  } catch (error) {
    console.error('❌ Erro ao verificar bolões:', error)
  }
}

checkBoloes()
