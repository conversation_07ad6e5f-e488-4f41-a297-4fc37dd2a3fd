import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function debugBilhetes() {
  try {
    console.log('🔍 Debugando bilhetes no banco de dados...')
    
    await initializeDatabase()

    // Buscar todos os bilhetes
    const bilhetes = await executeQuery(`
      SELECT id, codigo, transaction_id, status, valor_total, usuario_nome
      FROM bilhetes 
      ORDER BY id DESC
    `)

    console.log(`📊 Total de bilhetes encontrados: ${bilhetes.length}`)
    console.log('')

    bilhetes.forEach((bilhete, index) => {
      console.log(`${index + 1}. Bilhete ID: ${bilhete.id}`)
      console.log(`   Código: ${bilhete.codigo}`)
      console.log(`   Transaction ID: ${bilhete.transaction_id}`)
      console.log(`   Status: ${bilhete.status}`)
      console.log(`   Valor: R$ ${bilhete.valor_total}`)
      console.log(`   Usuário: ${bilhete.usuario_nome}`)
      console.log('')
    })

    // Testar busca específica pelos transaction_ids reais
    const transactionIds = [
      'pixi_01k08rvf1wfk9bg8z3t0zq1at9',
      'pixi_01k08rwejvf46s72a6bjk246fp'
    ]

    console.log('🔍 Testando busca por transaction_id...')
    
    for (const txId of transactionIds) {
      console.log(`\nBuscando por transaction_id: ${txId}`)
      
      const resultado = await executeQuery(`
        SELECT id, codigo, status, transaction_id 
        FROM bilhetes
        WHERE transaction_id = ?
        LIMIT 1
      `, [txId])

      if (resultado.length > 0) {
        console.log(`✅ Encontrado: ${resultado[0].codigo} - Status: ${resultado[0].status}`)
      } else {
        console.log(`❌ Não encontrado`)
      }
    }

    // Testar busca pelos códigos
    const codigos = [
      'BLT17526411592332151YS3W',
      'BLT175264119146122Z6GZDN'
    ]

    console.log('\n🔍 Testando busca por código...')
    
    for (const codigo of codigos) {
      console.log(`\nBuscando por código: ${codigo}`)
      
      const resultado = await executeQuery(`
        SELECT id, codigo, status, transaction_id 
        FROM bilhetes
        WHERE codigo = ?
        LIMIT 1
      `, [codigo])

      if (resultado.length > 0) {
        console.log(`✅ Encontrado: ${resultado[0].codigo} - Status: ${resultado[0].status}`)
      } else {
        console.log(`❌ Não encontrado`)
      }
    }

    // Testar a query exata do webhook
    console.log('\n🔍 Testando query exata do webhook...')
    
    for (const txId of transactionIds) {
      console.log(`\nTestando query webhook para: ${txId}`)
      
      const resultado = await executeQuery(`
        SELECT id, codigo, status, transaction_id FROM bilhetes
        WHERE transaction_id = ? OR codigo = ?
        LIMIT 1
      `, [txId, txId])

      if (resultado.length > 0) {
        console.log(`✅ Webhook encontraria: ${resultado[0].codigo} - Status: ${resultado[0].status}`)
      } else {
        console.log(`❌ Webhook não encontraria`)
      }
    }

  } catch (error) {
    console.error('❌ Erro ao debugar bilhetes:', error)
  }
}

debugBilhetes()
