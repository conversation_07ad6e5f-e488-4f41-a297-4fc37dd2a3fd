import mysql from "mysql2/promise"

async function testDirectConnection() {
  try {
    console.log('🔍 Testando conexão direta com o banco...')
    
    // Testar diferentes configurações de banco
    const configs = [
      {
        name: 'sistema-bolao-top',
        config: {
          host: 'localhost',
          port: 3306,
          user: 'root',
          password: '',
          database: 'sistema-bolao-top',
          charset: 'utf8mb4'
        }
      },
      {
        name: 'sistema-bolao-top (com senha)',
        config: {
          host: 'localhost',
          port: 3306,
          user: 'root',
          password: 'root',
          database: 'sistema-bolao-top',
          charset: 'utf8mb4'
        }
      },
      {
        name: 'localhost sem banco específico',
        config: {
          host: 'localhost',
          port: 3306,
          user: 'root',
          password: '',
          charset: 'utf8mb4'
        }
      }
    ]

    for (const { name, config } of configs) {
      console.log(`\n📡 Testando: ${name}`)
      
      try {
        const connection = await mysql.createConnection(config)
        console.log('   ✅ Conexão estabelecida')
        
        if (config.database) {
          // Verificar se a tabela bilhetes existe
          try {
            const [tables] = await connection.execute(`
              SELECT TABLE_NAME 
              FROM information_schema.tables 
              WHERE table_schema = ? AND table_name = 'bilhetes'
            `, [config.database])

            if (tables.length > 0) {
              console.log('   ✅ Tabela "bilhetes" encontrada!')
              
              // Contar registros
              const [count] = await connection.execute('SELECT COUNT(*) as total FROM bilhetes')
              console.log(`   📊 Total de registros: ${count[0].total}`)
              
              if (count[0].total > 0) {
                // Buscar os bilhetes específicos
                const [bilhetes] = await connection.execute(`
                  SELECT id, codigo, transaction_id, status, valor_total, usuario_nome
                  FROM bilhetes 
                  WHERE codigo IN ('BLT17526411592332151YS3W', 'BLT175264119146122Z6GZDN')
                  ORDER BY id DESC
                `)
                
                console.log(`   🎯 Bilhetes específicos encontrados: ${bilhetes.length}`)
                bilhetes.forEach((bilhete, index) => {
                  console.log(`     ${index + 1}. ${bilhete.codigo}`)
                  console.log(`        Transaction ID: ${bilhete.transaction_id}`)
                  console.log(`        Status: ${bilhete.status}`)
                  console.log(`        Valor: R$ ${bilhete.valor_total}`)
                  console.log(`        Usuário: ${bilhete.usuario_nome}`)
                })
                
                // Se encontrou os bilhetes, testar atualização
                if (bilhetes.length > 0) {
                  console.log('\n   🧪 Testando atualização de status...')
                  
                  const bilhete = bilhetes[0]
                  const novoStatus = bilhete.status === 'pendente' ? 'pago' : 'pendente'
                  
                  const [updateResult] = await connection.execute(`
                    UPDATE bilhetes 
                    SET status = ?, updated_at = NOW() 
                    WHERE id = ?
                  `, [novoStatus, bilhete.id])
                  
                  console.log(`   ✅ Atualização realizada: ${updateResult.affectedRows} linha(s) afetada(s)`)
                  
                  // Verificar se a atualização funcionou
                  const [verificacao] = await connection.execute(`
                    SELECT status FROM bilhetes WHERE id = ?
                  `, [bilhete.id])
                  
                  console.log(`   📊 Status atual: ${verificacao[0].status}`)
                  
                  // Reverter para o status original
                  await connection.execute(`
                    UPDATE bilhetes 
                    SET status = ?, updated_at = NOW() 
                    WHERE id = ?
                  `, [bilhete.status, bilhete.id])
                  
                  console.log(`   🔄 Status revertido para: ${bilhete.status}`)
                }
              }
            } else {
              console.log('   ❌ Tabela "bilhetes" não encontrada')
            }
          } catch (tableError) {
            console.log('   ❌ Erro ao verificar tabela:', tableError.message)
          }
        }
        
        await connection.end()
        
      } catch (connError) {
        console.log('   ❌ Erro de conexão:', connError.message)
      }
    }

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
  }
}

testDirectConnection()
