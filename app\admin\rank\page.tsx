'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Crown, Trophy, Medal, Star, TrendingUp } from 'lucide-react'

interface RankingUser {
  id: number
  nome: string
  email: string
  pontos_totais: number
  apostas_certas: number
  apostas_totais: number
  percentual_acerto: number
  posicao: number
}

export default function RankPage() {
  const [ranking, setRanking] = useState<RankingUser[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchRanking()
  }, [])

  const fetchRanking = async () => {
    try {
      setLoading(true)
      // Simular dados de ranking por enquanto
      const mockRanking: RankingUser[] = [
        {
          id: 1,
          nome: '<PERSON>',
          email: '<EMAIL>',
          pontos_totais: 9, // 1 ponto por acerto
          apostas_certas: 9,
          apostas_totais: 11,
          percentual_acerto: 82,
          posicao: 1
        },
        {
          id: 2,
          nome: '<PERSON>',
          email: '<EMAIL>',
          pontos_totais: 8, // 1 ponto por acerto
          apostas_certas: 8,
          apostas_totais: 11,
          percentual_acerto: 73,
          posicao: 2
        },
        {
          id: 3,
          nome: 'Pedro Costa',
          email: '<EMAIL>',
          pontos_totais: 7, // 1 ponto por acerto
          apostas_certas: 7,
          apostas_totais: 11,
          percentual_acerto: 64,
          posicao: 3
        },
        {
          id: 4,
          nome: 'Ana Oliveira',
          email: '<EMAIL>',
          pontos_totais: 6, // 1 ponto por acerto
          apostas_certas: 6,
          apostas_totais: 11,
          percentual_acerto: 55,
          posicao: 4
        },
        {
          id: 5,
          nome: 'Carlos Lima',
          email: '<EMAIL>',
          pontos_totais: 5, // 1 ponto por acerto
          apostas_certas: 5,
          apostas_totais: 11,
          percentual_acerto: 45,
          posicao: 5
        }
      ]
      
      setRanking(mockRanking)
    } catch (error) {
      console.error('Erro ao buscar ranking:', error)
    } finally {
      setLoading(false)
    }
  }

  const getRankIcon = (posicao: number) => {
    switch (posicao) {
      case 1:
        return <Crown className="h-6 w-6 text-yellow-500" />
      case 2:
        return <Trophy className="h-6 w-6 text-gray-400" />
      case 3:
        return <Medal className="h-6 w-6 text-amber-600" />
      default:
        return <Star className="h-6 w-6 text-blue-500" />
    }
  }

  const getRankColor = (posicao: number) => {
    switch (posicao) {
      case 1:
        return 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200'
      case 2:
        return 'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200'
      case 3:
        return 'bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200'
      default:
        return 'bg-white border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando ranking...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">🏆 Ranking de Usuários</h1>
          <p className="text-gray-600 mt-2">Classificação dos melhores apostadores</p>
        </div>
        <div className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5 text-green-600" />
          <span className="text-sm text-gray-600">Atualizado em tempo real</span>
        </div>
      </div>

      <div className="grid gap-4">
        {ranking.map((user) => (
          <Card key={user.id} className={`transition-all hover:shadow-lg ${getRankColor(user.posicao)}`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getRankIcon(user.posicao)}
                    <span className="text-2xl font-bold text-gray-700">#{user.posicao}</span>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{user.nome}</h3>
                    <p className="text-sm text-gray-600">{user.email}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-3xl font-bold text-green-600">{user.apostas_certas}/{user.apostas_totais}</p>
                    <p className="text-xs text-gray-500">Acertos</p>
                  </div>

                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{user.pontos_totais}</p>
                    <p className="text-xs text-gray-500">Pontos (1 por acerto)</p>
                  </div>

                  <div className="text-center">
                    <Badge variant={user.percentual_acerto >= 70 ? 'default' : user.percentual_acerto >= 50 ? 'secondary' : 'destructive'}>
                      {user.percentual_acerto}%
                    </Badge>
                    <p className="text-xs text-gray-500 mt-1">Taxa de Acerto</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>📊 Estatísticas Gerais</CardTitle>
          <CardDescription>Resumo do desempenho geral dos usuários</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{ranking.length}</p>
              <p className="text-sm text-gray-600">Usuários Ativos</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">
                {Math.round(ranking.reduce((acc, user) => acc + user.percentual_acerto, 0) / ranking.length)}%
              </p>
              <p className="text-sm text-gray-600">Taxa Média de Acerto</p>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <p className="text-2xl font-bold text-yellow-600">
                {ranking.reduce((acc, user) => acc + user.pontos_totais, 0)}
              </p>
              <p className="text-sm text-gray-600">Total de Pontos</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">
                {ranking.reduce((acc, user) => acc + user.apostas_totais, 0)}
              </p>
              <p className="text-sm text-gray-600">Total de Apostas</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
