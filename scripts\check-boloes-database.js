import mysql from "mysql2/promise"

async function checkBoloesDatabase() {
  try {
    console.log('🔍 Verificando bolões na base de dados...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })

    // Verificar bolões
    const [boloes] = await connection.execute(`
      SELECT 
        b.*,
        u.nome as criador_nome,
        (SELECT COUNT(*) FROM apostas WHERE bolao_id = b.id AND status = 'paga') as participantes,
        (SELECT COUNT(*) FROM bolao_jogos WHERE bolao_id = b.id) as total_jogos
      FROM boloes b
      LEFT JOIN usuarios u ON b.criado_por = u.id
      ORDER BY b.data_inicio DESC
    `)

    console.log(`📊 Total de bolões: ${boloes.length}`)
    
    if (boloes.length > 0) {
      console.log(`\n📋 Bolões encontrados:`)
      boloes.forEach((bolao, index) => {
        console.log(`  ${index + 1}. ${bolao.nome} - Status: ${bolao.status} - Jogos: ${bolao.total_jogos}`)
      })
      
      // Verificar jogos associados ao primeiro bolão
      const primeiroBolao = boloes[0]
      console.log(`\n🎯 Verificando jogos do bolão: ${primeiroBolao.nome}`)
      
      const [jogosAssociados] = await connection.execute(`
        SELECT 
          bj.bolao_id,
          bj.jogo_id,
          j.data_jogo,
          j.status,
          tc.nome as time_casa_nome,
          tf.nome as time_fora_nome,
          c.nome as campeonato_nome
        FROM bolao_jogos bj
        JOIN jogos j ON bj.jogo_id = j.id
        JOIN times tc ON j.time_casa_id = tc.id
        JOIN times tf ON j.time_fora_id = tf.id
        JOIN campeonatos c ON j.campeonato_id = c.id
        WHERE bj.bolao_id = ?
        ORDER BY j.data_jogo ASC
      `, [primeiroBolao.id])
      
      console.log(`⚽ Jogos associados: ${jogosAssociados.length}`)
      
      if (jogosAssociados.length > 0) {
        console.log(`\n🏆 Campeonatos no bolão:`)
        const campeonatos = [...new Set(jogosAssociados.map(j => j.campeonato_nome))]
        campeonatos.forEach((campeonato, index) => {
          const jogosNoCampeonato = jogosAssociados.filter(j => j.campeonato_nome === campeonato)
          console.log(`  ${index + 1}. ${campeonato}: ${jogosNoCampeonato.length} jogos`)
        })
        
        console.log(`\n⚽ Primeiros jogos:`)
        jogosAssociados.slice(0, 5).forEach((jogo, index) => {
          console.log(`  ${index + 1}. ${jogo.time_casa_nome} vs ${jogo.time_fora_nome} - ${jogo.campeonato_nome}`)
        })
        
        // Agora vamos buscar TODOS os jogos desses campeonatos
        console.log(`\n🔍 Buscando TODOS os jogos dos campeonatos selecionados...`)
        
        const campeonatosIds = [...new Set(jogosAssociados.map(j => j.campeonato_nome))]
        
        for (const campeonato of campeonatosIds) {
          const [todosJogosCampeonato] = await connection.execute(`
            SELECT 
              j.id,
              j.data_jogo,
              j.status,
              tc.nome as time_casa_nome,
              tf.nome as time_fora_nome,
              c.nome as campeonato_nome
            FROM jogos j
            JOIN times tc ON j.time_casa_id = tc.id
            JOIN times tf ON j.time_fora_id = tf.id
            JOIN campeonatos c ON j.campeonato_id = c.id
            WHERE c.nome = ?
            AND j.status IN ('agendado', 'ao_vivo', 'finalizado')
            ORDER BY j.data_jogo ASC
          `, [campeonato])
          
          console.log(`\n🏆 ${campeonato}: ${todosJogosCampeonato.length} jogos totais`)
          
          if (todosJogosCampeonato.length > 0) {
            console.log(`   Primeiros jogos:`)
            todosJogosCampeonato.slice(0, 3).forEach((jogo, index) => {
              const data = new Date(jogo.data_jogo).toLocaleDateString('pt-BR')
              console.log(`     ${index + 1}. ${jogo.time_casa_nome} vs ${jogo.time_fora_nome} - ${data}`)
            })
          }
        }
        
      } else {
        console.log(`❌ Nenhum jogo associado ao bolão`)
      }
      
    } else {
      console.log(`❌ Nenhum bolão encontrado`)
    }

    // Verificar se há jogos na tabela jogos
    const [totalJogos] = await connection.execute(`
      SELECT COUNT(*) as total FROM jogos
    `)
    
    console.log(`\n📊 Total de jogos na base: ${totalJogos[0].total}`)

    // Verificar campeonatos
    const [campeonatos] = await connection.execute(`
      SELECT 
        c.nome,
        COUNT(j.id) as total_jogos
      FROM campeonatos c
      LEFT JOIN jogos j ON c.id = j.campeonato_id
      GROUP BY c.id, c.nome
      ORDER BY total_jogos DESC
    `)
    
    console.log(`\n🏆 Campeonatos disponíveis:`)
    campeonatos.forEach((campeonato, index) => {
      console.log(`  ${index + 1}. ${campeonato.nome}: ${campeonato.total_jogos} jogos`)
    })

    await connection.end()
    
    console.log(`\n✅ Verificação concluída!`)

  } catch (error) {
    console.error('❌ Erro na verificação:', error.message)
  }
}

checkBoloesDatabase()
