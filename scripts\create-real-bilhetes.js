import mysql from "mysql2/promise"

async function createRealBilhetes() {
  try {
    console.log('🎫 Criando bilhetes reais no banco...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })

    // Usar o usuário ID 18 que já existe
    const usuarioId = 18

    // Criar os bilhetes reais
    const bilhetes = [
      {
        codigo: 'BLT17526411592332151YS3W',
        transaction_id: 'pixi_01k08rvf1wfk9bg8z3t0zq1at9',
        usuario_nome: 'moramed',
        usuario_email: '<EMAIL>',
        usuario_cpf: '762.368.020-09',
        valor_total: 0.60,
        quantidade_apostas: 11
      },
      {
        codigo: 'BLT175264119146122Z6GZDN',
        transaction_id: 'pixi_01k08rwejvf46s72a6bjk246fp',
        usuario_nome: 'Teste rr',
        usuario_email: 'francildo<PERSON><PERSON><EMAIL>',
        usuario_cpf: '413.852.658-76',
        valor_total: 0.60,
        quantidade_apostas: 11
      }
    ]

    console.log('💾 Inserindo bilhetes...')
    
    const bilhetesInseridos = []

    for (const bilhete of bilhetes) {
      try {
        const [insertResult] = await connection.execute(`
          INSERT INTO bilhetes (
            codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
            valor_total, quantidade_apostas, status, qr_code_pix, transaction_id,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          bilhete.codigo,
          usuarioId,
          bilhete.usuario_nome,
          bilhete.usuario_email,
          bilhete.usuario_cpf,
          bilhete.valor_total,
          bilhete.quantidade_apostas,
          'pendente',
          '00020126810014br.gov.bcb.pix2559qr-code.picpay.com...',
          bilhete.transaction_id
        ])

        bilhetesInseridos.push({
          id: insertResult.insertId,
          codigo: bilhete.codigo,
          transaction_id: bilhete.transaction_id
        })

        console.log(`✅ Bilhete ${bilhete.codigo} inserido com ID: ${insertResult.insertId}`)
        
      } catch (insertError) {
        console.error(`❌ Erro ao inserir bilhete ${bilhete.codigo}:`, insertError.message)
      }
    }

    // Verificar bilhetes inseridos
    console.log('\n📊 Verificando bilhetes no banco...')
    const [bilhetesVerificacao] = await connection.execute(`
      SELECT id, codigo, transaction_id, status, valor_total, usuario_nome FROM bilhetes
    `)

    console.log(`Total de bilhetes: ${bilhetesVerificacao.length}`)
    bilhetesVerificacao.forEach((bilhete, index) => {
      console.log(`  ${index + 1}. ID: ${bilhete.id} - ${bilhete.codigo} - ${bilhete.transaction_id} - ${bilhete.status} - R$ ${bilhete.valor_total} - ${bilhete.usuario_nome}`)
    })

    // Testar webhook com os bilhetes
    console.log('\n🔗 Testando webhook com bilhetes reais...')
    
    for (const bilhete of bilhetesInseridos) {
      console.log(`\n💰 Testando pagamento do bilhete ${bilhete.codigo}...`)
      
      const webhookData = {
        order_id: bilhete.transaction_id,
        transaction_id: bilhete.transaction_id,
        status: 'PAID',
        type: 'payment',
        message: 'Pagamento aprovado',
        amount: 0.60,
        payment_method: 'pix'
      }

      try {
        const webhookResponse = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(webhookData)
        })

        const webhookResult = await webhookResponse.json()
        
        console.log(`   Status HTTP: ${webhookResponse.status}`)
        console.log(`   Resposta: ${webhookResult.message || webhookResult.error || 'N/A'}`)
        
        // Verificar se o status foi atualizado
        const [statusAtualizado] = await connection.execute(`
          SELECT status FROM bilhetes WHERE id = ?
        `, [bilhete.id])
        
        console.log(`   Status no banco: ${statusAtualizado[0].status}`)
        
        if (statusAtualizado[0].status === 'pago') {
          console.log(`   ✅ WEBHOOK FUNCIONOU! Bilhete marcado como PAGO`)
        } else {
          console.log(`   ❌ Webhook não atualizou o status`)
        }
        
      } catch (webhookError) {
        console.error(`   ❌ Erro no webhook: ${webhookError.message}`)
      }
    }

    // Verificar logs de webhook
    console.log('\n📋 Verificando logs de webhook...')
    const [logs] = await connection.execute(`
      SELECT id, transaction_id, status, processed_at FROM webhook_logs 
      ORDER BY processed_at DESC 
      LIMIT 5
    `)

    console.log(`Logs de webhook: ${logs.length}`)
    logs.forEach((log, index) => {
      console.log(`  ${index + 1}. ${log.transaction_id} - ${log.status} - ${log.processed_at}`)
    })

    await connection.end()
    
    console.log('\n🎉 TESTE CONCLUÍDO!')
    console.log('Os bilhetes foram criados e o webhook foi testado.')
    console.log('Se o status foi atualizado para "pago", o webhook está funcionando corretamente!')

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
  }
}

createRealBilhetes()
