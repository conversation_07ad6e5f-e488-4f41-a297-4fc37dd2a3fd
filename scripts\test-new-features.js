// Testar as novas funcionalidades implementadas

async function testNewFeatures() {
  try {
    console.log('🧪 Testando novas funcionalidades...')
    
    // 1. Testar API de bilhetes de usuário
    console.log('\n1️⃣ Testando API de bilhetes de usuário...')
    
    try {
      const response = await fetch('http://localhost:3000/api/admin/usuarios/18/bilhetes')
      const data = await response.json()
      
      console.log(`   Status: ${response.status}`)
      if (response.ok) {
        console.log(`   ✅ Bilhetes encontrados: ${data.bilhetes?.length || 0}`)
        console.log(`   📊 Stats:`)
        console.log(`      - Total: ${data.stats?.total_bilhetes || 0}`)
        console.log(`      - Pagos: ${data.stats?.bilhetes_pagos || 0}`)
        console.log(`      - Pendentes: ${data.stats?.bilhetes_pendentes || 0}`)
        console.log(`      - Valor total: R$ ${data.stats?.valor_total || 0}`)
        
        if (data.bilhetes && data.bilhetes.length > 0) {
          console.log(`   📋 Primeiro bilhete:`)
          const bilhete = data.bilhetes[0]
          console.log(`      - Código: ${bilhete.codigo}`)
          console.log(`      - Status: ${bilhete.status}`)
          console.log(`      - Apostas: ${bilhete.total_apostas}`)
          console.log(`      - Valor: R$ ${bilhete.valor_total}`)
        }
      } else {
        console.log(`   ❌ Erro: ${data.error}`)
      }
    } catch (error) {
      console.log(`   ❌ Erro na requisição: ${error.message}`)
    }

    // 2. Testar API de bolões (separação por campeonato)
    console.log('\n2️⃣ Testando API de bolões (separação por campeonato)...')
    
    try {
      const response = await fetch('http://localhost:3000/api/boloes')
      const data = await response.json()
      
      console.log(`   Status: ${response.status}`)
      if (response.ok) {
        console.log(`   ✅ Bolões encontrados: ${data.boloes?.length || 0}`)
        
        if (data.boloes && data.boloes.length > 0) {
          const bolao = data.boloes[0]
          console.log(`   📋 Primeiro bolão:`)
          console.log(`      - Nome: ${bolao.nome}`)
          console.log(`      - Jogos: ${bolao.jogos?.length || 0}`)
          
          if (bolao.jogos && bolao.jogos.length > 0) {
            // Agrupar jogos por campeonato
            const jogosPorCampeonato = bolao.jogos.reduce((acc, jogo) => {
              const campeonato = jogo.campeonato_nome
              if (!acc[campeonato]) {
                acc[campeonato] = []
              }
              acc[campeonato].push(jogo)
              return acc
            }, {})
            
            console.log(`   🏆 Campeonatos no bolão:`)
            Object.entries(jogosPorCampeonato).forEach(([campeonato, jogos]) => {
              console.log(`      - ${campeonato}: ${jogos.length} jogos`)
            })
            
            console.log(`   ⚽ Exemplo de jogo:`)
            const jogo = bolao.jogos[0]
            console.log(`      - ${jogo.time_casa_nome} vs ${jogo.time_fora_nome}`)
            console.log(`      - Campeonato: ${jogo.campeonato_nome}`)
            console.log(`      - Data: ${jogo.data_jogo}`)
          }
        }
      } else {
        console.log(`   ❌ Erro: ${data.error}`)
      }
    } catch (error) {
      console.log(`   ❌ Erro na requisição: ${error.message}`)
    }

    // 3. Testar estrutura de bilhetes e apostas
    console.log('\n3️⃣ Testando estrutura de bilhetes e apostas...')
    
    try {
      const response = await fetch('http://localhost:3000/api/webhook/status')
      const data = await response.json()
      
      console.log(`   Status: ${response.status}`)
      if (response.ok) {
        console.log(`   ✅ Status do sistema:`)
        console.log(`      - Bilhetes recentes: ${data.bilhetes_recentes?.length || 0}`)
        console.log(`      - Valor total: R$ ${data.valor_total || 0}`)
        
        if (data.bilhetes_recentes && data.bilhetes_recentes.length > 0) {
          console.log(`   📋 Bilhetes recentes:`)
          data.bilhetes_recentes.forEach((bilhete, index) => {
            console.log(`      ${index + 1}. ${bilhete.codigo} - ${bilhete.status} - R$ ${bilhete.valor}`)
          })
        }
      } else {
        console.log(`   ❌ Erro: ${data.error}`)
      }
    } catch (error) {
      console.log(`   ❌ Erro na requisição: ${error.message}`)
    }

    // 4. Testar logs de webhook
    console.log('\n4️⃣ Testando logs de webhook...')
    
    try {
      const response = await fetch('http://localhost:3000/api/pix/logs')
      const data = await response.json()
      
      console.log(`   Status: ${response.status}`)
      if (response.ok) {
        console.log(`   ✅ Logs de webhook:`)
        console.log(`      - Total de logs: ${data.logs?.length || 0}`)
        
        if (data.stats) {
          console.log(`   📊 Estatísticas:`)
          Object.entries(data.stats.por_status || {}).forEach(([status, count]) => {
            console.log(`      - ${status}: ${count}`)
          })
        }
        
        if (data.logs && data.logs.length > 0) {
          console.log(`   📋 Últimos logs:`)
          data.logs.slice(0, 3).forEach((log, index) => {
            console.log(`      ${index + 1}. ${log.transaction_id} - ${log.status} - ${log.processed_at}`)
          })
        }
      } else {
        console.log(`   ❌ Erro: ${data.error}`)
      }
    } catch (error) {
      console.log(`   ❌ Erro na requisição: ${error.message}`)
    }

    console.log('\n✅ Teste das novas funcionalidades concluído!')
    console.log('\n📋 Resumo das funcionalidades implementadas:')
    console.log('   1. ✅ API para buscar bilhetes de usuário específico')
    console.log('   2. ✅ Modal para visualizar bilhetes e apostas do usuário')
    console.log('   3. ✅ Botão na tabela de usuários para ver bilhetes')
    console.log('   4. ✅ Separação de jogos por campeonato (já funcionando)')
    console.log('   5. ✅ Sistema de webhook funcionando automaticamente')
    
    console.log('\n🎯 Como usar:')
    console.log('   • No painel admin, vá em "Usuários"')
    console.log('   • Clique no botão azul (ícone de recibo) para ver bilhetes do usuário')
    console.log('   • Os jogos na página principal são separados por campeonato automaticamente')
    console.log('   • O webhook processa pagamentos automaticamente')

  } catch (error) {
    console.error('❌ Erro geral no teste:', error.message)
  }
}

testNewFeatures()
