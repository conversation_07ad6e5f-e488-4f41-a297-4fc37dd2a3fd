// Testar criação de bolão

async function testCreateBolao() {
  try {
    console.log('🧪 Testando criação de bolão...')
    
    const dadosBolao = {
      nome: 'Bolão Teste',
      descricao: 'Teste de criação',
      valor_aposta: 25.00,
      premio_total: 1000.00,
      data_inicio: new Date().toISOString(),
      data_fim: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      max_participantes: 100,
      banner_image: '/uploads/banners/test.webp',
      campeonatos_selecionados: [
        { id: 1, nome: 'Brasileirão' }
      ],
      partidas_selecionadas: []
    }
    
    console.log('📦 Dados do bolão:', {
      nome: dadosBolao.nome,
      valor_aposta: dadosBolao.valor_aposta,
      premio_total: dadosBolao.premio_total
    })
    
    const response = await fetch('http://localhost:3000/api/admin/boloes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dadosBolao)
    })
    
    const result = await response.json()
    
    console.log(`📡 Status: ${response.status}`)
    console.log(`📋 Resultado:`, result)
    
    if (response.ok) {
      console.log('✅ Criação funcionou!')
    } else {
      console.log('❌ Erro na criação:')
      console.log(`   Erro: ${result.error}`)
      console.log(`   Mensagem: ${result.message}`)
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error.message)
  }
}

testCreateBolao()
