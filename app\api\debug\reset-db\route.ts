import { NextResponse } from "next/server"
import { closeDatabase, initializeDatabase } from "@/lib/database-config"

export async function POST() {
  try {
    console.log("🔄 Resetando conexões do banco de dados...")
    
    // <PERSON><PERSON>r todas as conexões existentes
    await closeDatabase()
    console.log("✅ Conexões fechadas")
    
    // Aguardar um pouco
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Reinicializar o pool
    await initializeDatabase()
    console.log("✅ Pool reinicializado")
    
    return NextResponse.json({
      success: true,
      message: "Conexões do banco resetadas com sucesso"
    })
    
  } catch (error) {
    console.error("❌ Erro ao resetar conexões:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message
      },
      { status: 500 }
    )
  }
}
