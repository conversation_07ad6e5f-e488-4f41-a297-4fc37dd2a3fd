import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function createSampleBoloes() {
  try {
    console.log('🎯 Criando bolões de exemplo com dados reais...')
    
    await initializeDatabase()

    // Buscar alguns jogos reais do banco
    const jogos = await executeQuery(`
      SELECT j.*, 
             tc.nome as time_casa_nome, 
             tf.nome as time_fora_nome,
             c.nome as campeonato_nome
      FROM jogos j
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE j.data_jogo > NOW()
      ORDER BY j.data_jogo ASC
      LIMIT 20
    `)

    if (jogos.length === 0) {
      console.log('⚠️ Nenhum jogo encontrado. Criando jogos de exemplo...')
      
      // Buscar times brasileiros
      const timesBrasileiros = await executeQuery(`
        SELECT * FROM times 
        WHERE pais = 'Brazil' 
        ORDER BY nome 
        LIMIT 10
      `)

      // Buscar campeonato brasileiro
      const campeonatoBrasileiro = await executeQuery(`
        SELECT * FROM campeonatos 
        WHERE nome LIKE '%Brasil%' 
        LIMIT 1
      `)

      if (timesBrasileiros.length >= 4 && campeonatoBrasileiro.length > 0) {
        const campeonato_id = campeonatoBrasileiro[0].id
        
        // Criar alguns jogos de exemplo
        const jogosExemplo = [
          {
            time_casa_id: timesBrasileiros[0].id,
            time_fora_id: timesBrasileiros[1].id,
            data_jogo: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
          },
          {
            time_casa_id: timesBrasileiros[2].id,
            time_fora_id: timesBrasileiros[3].id,
            data_jogo: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
          },
          {
            time_casa_id: timesBrasileiros[4].id,
            time_fora_id: timesBrasileiros[5].id,
            data_jogo: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
          },
          {
            time_casa_id: timesBrasileiros[6].id,
            time_fora_id: timesBrasileiros[7].id,
            data_jogo: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
          },
          {
            time_casa_id: timesBrasileiros[8].id,
            time_fora_id: timesBrasileiros[9].id,
            data_jogo: new Date(Date.now() + 11 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
          }
        ]

        for (const jogo of jogosExemplo) {
          await executeQuery(`
            INSERT INTO jogos (time_casa_id, time_fora_id, campeonato_id, data_jogo, status)
            VALUES (?, ?, ?, ?, 'agendado')
          `, [jogo.time_casa_id, jogo.time_fora_id, campeonato_id, jogo.data_jogo])
        }

        console.log('✅ Jogos de exemplo criados')
      }
    }

    // Buscar jogos novamente
    const jogosDisponiveis = await executeQuery(`
      SELECT j.*, 
             tc.nome as time_casa_nome, 
             tf.nome as time_fora_nome,
             c.nome as campeonato_nome
      FROM jogos j
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE j.data_jogo > NOW()
      ORDER BY j.data_jogo ASC
      LIMIT 10
    `)

    if (jogosDisponiveis.length === 0) {
      console.log('❌ Não foi possível encontrar jogos para criar bolões')
      return
    }

    // Criar bolões de exemplo
    const boloes = [
      {
        nome: 'Brasileirão 2024 - Rodada Especial',
        descricao: 'Apostas nos principais jogos do Campeonato Brasileiro. Acerte o resultado de pelo menos 3 jogos para concorrer aos prêmios!',
        valor_aposta: 25.00,
        premio_total: 5000.00,
        max_participantes: 500,
        min_acertos: 3,
        data_inicio: new Date().toISOString().split('T')[0],
        data_fim: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        jogos: jogosDisponiveis.slice(0, 5)
      },
      {
        nome: 'Copa Elite - Grandes Clássicos',
        descricao: 'Os maiores clássicos do futebol mundial. Prove que você entende de futebol!',
        valor_aposta: 50.00,
        premio_total: 10000.00,
        max_participantes: 200,
        min_acertos: 4,
        data_inicio: new Date().toISOString().split('T')[0],
        data_fim: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        jogos: jogosDisponiveis.slice(2, 6)
      },
      {
        nome: 'Bolão Iniciante - Fácil',
        descricao: 'Perfeito para quem está começando. Apostas com valor baixo e boas chances de ganhar!',
        valor_aposta: 10.00,
        premio_total: 1000.00,
        max_participantes: 1000,
        min_acertos: 2,
        data_inicio: new Date().toISOString().split('T')[0],
        data_fim: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        jogos: jogosDisponiveis.slice(0, 3)
      }
    ]

    for (const bolao of boloes) {
      // Inserir bolão
      const result = await executeQuery(`
        INSERT INTO boloes (
          nome, descricao, valor_aposta, premio_total, max_participantes,
          min_acertos, data_inicio, data_fim, status, criado_por, regras
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'ativo', 1, ?)
      `, [
        bolao.nome,
        bolao.descricao,
        bolao.valor_aposta,
        bolao.premio_total,
        bolao.max_participantes,
        bolao.min_acertos,
        bolao.data_inicio,
        bolao.data_fim,
        JSON.stringify([
          "Acerte pelo menos " + bolao.min_acertos + " resultados para ganhar prêmios",
          "3 acertos: 10% do prêmio total",
          "4 acertos: 25% do prêmio total",
          "5 acertos: 65% do prêmio total",
          "Apostas devem ser feitas até 1 hora antes do primeiro jogo",
          "Resultados são baseados no tempo regulamentar (90 minutos)"
        ])
      ])

      const bolao_id = result.insertId

      // Inserir jogos do bolão
      for (const jogo of bolao.jogos) {
        await executeQuery(`
          INSERT INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolao_id, jogo.id])
      }

      console.log(`✅ Bolão criado: ${bolao.nome} (ID: ${bolao_id})`)
    }

    console.log('🎯 Bolões de exemplo criados com sucesso!')

  } catch (error) {
    console.error('❌ Erro ao criar bolões de exemplo:', error)
  }
}

createSampleBoloes()
