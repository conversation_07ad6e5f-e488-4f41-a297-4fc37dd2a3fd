import mysql from "mysql2/promise"

let db = null
let pool = null

// Configuração do banco de dados MySQL
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top',
  charset: 'utf8mb4',
  timezone: '+00:00',
  connectionLimit: 3, // Reduzido para evitar "too many connections"
  queueLimit: 0,
  idleTimeout: 300000, // 5 minutos
  maxIdle: 1 // Máximo de conexões idle
}

// Função para inicializar o pool de conexões MySQL
export async function initializeDatabase() {
  try {
    if (!pool) {
      pool = mysql.createPool(dbConfig)
      console.log("✅ Pool de conexões MySQL inicializado com sucesso")
    }

    // Testar a conexão
    const connection = await pool.getConnection()
    await connection.ping()
    connection.release()

    console.log("✅ Conexão com MySQL estabelecida com sucesso")
    return pool
  } catch (error) {
    console.error("❌ Erro ao inicializar banco de dados MySQL:", error)
    throw error
  }
}

// Função para obter o pool de conexões
export async function getDatabase() {
  if (!pool) {
    await initializeDatabase()
  }
  return pool
}

// Função para executar queries com tratamento de erro
export async function executeQuery(query, params = []) {
  try {
    // Validar parâmetros para evitar undefined
    const safeParams = params.map(param => {
      if (param === undefined) {
        console.warn("⚠️ Parâmetro undefined detectado, convertendo para null")
        return null
      }
      return param
    })

    // Log para debug
    if (safeParams.some(p => p === null && params.some(p => p === undefined))) {
      console.log("🔍 Parâmetros originais:", params)
      console.log("🔍 Parâmetros seguros:", safeParams)
    }

    const connection = await getDatabase()
    const [results] = await connection.execute(query, safeParams)
    return results
  } catch (error) {
    console.error("❌ Erro ao executar query:", error)
    console.error("Query:", query)
    console.error("Params originais:", params)
    console.error("Params processados:", params.map(p => p === undefined ? 'UNDEFINED' : p))
    throw error
  }
}

// Função para executar queries que retornam uma única linha
export async function executeQuerySingle(query, params = []) {
  try {
    const results = await executeQuery(query, params)
    return results.length > 0 ? results[0] : null
  } catch (error) {
    console.error("❌ Erro ao executar query single:", error)
    throw error
  }
}

// Função para verificar se o banco de dados está configurado
export async function checkDatabaseSetup() {
  try {
    const connection = await getDatabase()

    // Verificar se as tabelas principais existem
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME IN ('usuarios', 'campeonatos', 'boloes', 'apostas', 'bolao_jogos')
    `, [process.env.DB_NAME || 'sistema-bolao-top'])

    const requiredTables = ['usuarios', 'campeonatos', 'boloes', 'apostas', 'bolao_jogos']
    const existingTables = tables.map(t => t.TABLE_NAME)
    const missingTables = requiredTables.filter(table => !existingTables.includes(table))

    if (missingTables.length > 0) {
      console.warn("⚠️ Tabelas faltando no banco de dados:", missingTables)

      // Criar tabela bolao_jogos se não existir
      if (missingTables.includes('bolao_jogos')) {
        try {
          await connection.execute(`
            CREATE TABLE IF NOT EXISTS bolao_jogos (
              id INT AUTO_INCREMENT PRIMARY KEY,
              bolao_id INT NOT NULL,
              jogo_id INT NOT NULL,
              data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              INDEX idx_bolao_id (bolao_id),
              INDEX idx_jogo_id (jogo_id),
              UNIQUE KEY unique_bolao_jogo (bolao_id, jogo_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
          `)
          console.log("✅ Tabela bolao_jogos criada com sucesso")
        } catch (error) {
          console.error("❌ Erro ao criar tabela bolao_jogos:", error)
        }
      }

      console.warn("Execute o script scripts/mysql-setup.sql para criar as demais tabelas")
      return false
    }

    console.log("✅ Todas as tabelas necessárias estão presentes")
    return true
  } catch (error) {
    console.error("❌ Erro ao verificar configuração do banco:", error)
    return false
  }
}



// ==================== FUNÇÕES PARA USUÁRIOS ====================

export async function getUsuarios(filters = {}) {
  try {
    let query = "SELECT * FROM usuarios WHERE 1=1"
    const params = []

    if (filters.status && filters.status !== "todos") {
      query += " AND status = ?"
      params.push(filters.status)
    }

    if (filters.tipo && filters.tipo !== "todos") {
      query += " AND tipo = ?"
      params.push(filters.tipo)
    }

    if (filters.search) {
      query += " AND (nome LIKE ? OR email LIKE ?)"
      params.push(`%${filters.search}%`, `%${filters.search}%`)
    }

    query += " ORDER BY data_cadastro DESC"

    const usuarios = await executeQuery(query, params)
    return usuarios || []
  } catch (error) {
    console.error("Erro ao buscar usuários:", error)
    return []
  }
}

export async function getUsuariosStats() {
  try {
    const [total, ativos, cambistas, bloqueados] = await Promise.all([
      executeQuerySingle("SELECT COUNT(*) as count FROM usuarios").catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE status = "ativo"').catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE tipo = "cambista"').catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE status = "bloqueado"').catch(() => ({ count: 0 })),
    ])

    return {
      total: total?.count || 0,
      ativos: ativos?.count || 0,
      cambistas: cambistas?.count || 0,
      bloqueados: bloqueados?.count || 0,
    }
  } catch (error) {
    console.error("Erro ao buscar stats de usuários:", error)
    return { total: 0, ativos: 0, cambistas: 0, bloqueados: 0 }
  }
}

// ==================== FUNÇÕES PARA CAMBISTAS ====================

export async function getCambistas(filters = {}) {
  try {
    let query = 'SELECT * FROM usuarios WHERE tipo = "cambista"'
    const params = []

    if (filters.search) {
      query += " AND (nome LIKE ? OR email LIKE ?)"
      params.push(`%${filters.search}%`, `%${filters.search}%`)
    }

    query += " ORDER BY data_cadastro DESC"

    const cambistas = await executeQuery(query, params)
    return cambistas || []
  } catch (error) {
    console.error("Erro ao buscar cambistas:", error)
    return []
  }
}

// ==================== FUNÇÕES PARA AFILIADOS ====================

export async function getAfiliados(filters = {}) {
  try {
    let query = `
      SELECT
        a.*,
        u.nome as nome_usuario,
        u.email as email_usuario,
        u.status as status_usuario
      FROM afiliados a
      LEFT JOIN usuarios u ON a.usuario_id = u.id
      WHERE 1=1
    `
    const params = []

    if (filters.status && filters.status !== "todos") {
      query += " AND a.status = ?"
      params.push(filters.status)
    }

    if (filters.search) {
      query += " AND (a.nome LIKE ? OR a.email LIKE ? OR a.codigo_afiliado LIKE ?)"
      params.push(`%${filters.search}%`, `%${filters.search}%`, `%${filters.search}%`)
    }

    query += " ORDER BY a.data_cadastro DESC"

    const afiliados = await executeQuery(query, params)
    return afiliados || []
  } catch (error) {
    console.error("Erro ao buscar afiliados:", error)
    return []
  }
}

export async function getAfiliadosStats() {
  try {
    const [total, ativos, inativos, comissoes] = await Promise.all([
      executeQuerySingle("SELECT COUNT(*) as count FROM afiliados").catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM afiliados WHERE status = "ativo"').catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM afiliados WHERE status = "inativo"').catch(() => ({ count: 0 })),
      executeQuerySingle(`
        SELECT
          COALESCE(SUM(comissao_total), 0) as total_comissoes,
          COALESCE(SUM(CASE WHEN DATE(data_cadastro) = CURDATE() THEN comissao_total ELSE 0 END), 0) as comissoes_hoje
        FROM afiliados
      `).catch(() => ({ total_comissoes: 0, comissoes_hoje: 0 })),
    ])

    return {
      total: total?.count || 0,
      ativos: ativos?.count || 0,
      inativos: inativos?.count || 0,
      totalComissoes: comissoes?.total_comissoes || 0,
      comissoesHoje: comissoes?.comissoes_hoje || 0,
    }
  } catch (error) {
    console.error("Erro ao buscar stats de afiliados:", error)
    return { total: 0, ativos: 0, inativos: 0, totalComissoes: 0, comissoesHoje: 0 }
  }
}

export async function createAfiliado(data) {
  try {
    // Verificar se o email já existe
    const existingAfiliado = await executeQuerySingle("SELECT id FROM afiliados WHERE email = ?", [data.email])

    if (existingAfiliado) {
      throw new Error("Email já cadastrado como afiliado")
    }

    // Gerar código único do afiliado
    const codigoAfiliado = `AF${Date.now().toString().slice(-6)}`

    // Hash da senha (em produção, use bcrypt)
    const senhaHash = `$2b$10$${data.senha}_hash_exemplo`

    const result = await executeQuery(
      `INSERT INTO afiliados (nome, email, telefone, codigo_afiliado, percentual_comissao, cpa_valor, tipo_comissao, senha_hash, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, "ativo")`,
      [
        data.nome,
        data.email,
        data.telefone,
        codigoAfiliado,
        data.percentual_comissao || 5,
        data.cpa_valor || 0,
        data.tipo_comissao || "percentual",
        senhaHash
      ],
    )

    return result.insertId
  } catch (error) {
    console.error("Erro ao criar afiliado:", error)
    throw error
  }
}

export async function updateAfiliado(id, data) {
  try {
    const updates = []
    const params = []

    if (data.nome) {
      updates.push("nome = ?")
      params.push(data.nome)
    }
    if (data.email) {
      updates.push("email = ?")
      params.push(data.email)
    }
    if (data.telefone) {
      updates.push("telefone = ?")
      params.push(data.telefone)
    }
    if (data.percentual_comissao !== undefined) {
      updates.push("percentual_comissao = ?")
      params.push(data.percentual_comissao)
    }
    if (data.status) {
      updates.push("status = ?")
      params.push(data.status)
    }
    if (data.senha) {
      const senhaHash = `$2b$10$${data.senha}_hash_exemplo`
      updates.push("senha_hash = ?")
      params.push(senhaHash)
    }

    if (updates.length === 0) {
      throw new Error("Nenhum campo para atualizar")
    }

    updates.push("data_atualizacao = NOW()")
    params.push(id)

    const query = `UPDATE afiliados SET ${updates.join(", ")} WHERE id = ?`
    await executeQuery(query, params)

    return true
  } catch (error) {
    console.error("Erro ao atualizar afiliado:", error)
    throw error
  }
}

export async function deleteAfiliado(id) {
  try {
    await executeQuery("DELETE FROM afiliados WHERE id = ?", [id])
    return true
  } catch (error) {
    console.error("Erro ao deletar afiliado:", error)
    throw error
  }
}

export async function createCambista(data) {
  try {
    // Verificar se o email já existe
    const existingUser = await executeQuerySingle("SELECT id FROM usuarios WHERE email = ?", [data.email])

    if (existingUser) {
      throw new Error("Email já cadastrado no sistema")
    }

    // Hash da senha (em produção, use bcrypt)
    const senhaHash = `$2b$10$${data.senha}_hash_exemplo`

    const result = await executeQuery(
      `INSERT INTO usuarios (nome, email, telefone, endereco, cpf_cnpj, senha_hash, tipo, status)
       VALUES (?, ?, ?, ?, ?, ?, "cambista", "ativo")`,
      [data.nome, data.email, data.telefone, data.endereco || null, data.cpf_cnpj || null, senhaHash],
    )

    return result.insertId
  } catch (error) {
    console.error("Erro ao criar cambista:", error)
    throw error
  }
}

export async function getCambistasStats() {
  try {
    const [ativos, vendas] = await Promise.all([
      executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE tipo = "cambista" AND status = "ativo"')
        .catch(() => ({ count: 0 })),
      executeQuerySingle(`
        SELECT
          COALESCE(SUM(a.valor_total), 0) as total_vendas,
          COALESCE(SUM(CASE WHEN DATE(a.data_aposta) = CURDATE() THEN a.valor_total ELSE 0 END), 0) as vendas_hoje
        FROM apostas a
        JOIN usuarios u ON a.usuario_id = u.id
        WHERE u.tipo = 'cambista'
      `)
        .catch(() => ({ total_vendas: 0, vendas_hoje: 0 })),
    ])

    const totalVendas = vendas?.total_vendas || 0
    const vendasHoje = vendas?.vendas_hoje || 0

    return {
      ativos: ativos?.count || 0,
      totalVendas: totalVendas,
      vendasHoje: vendasHoje,
      totalComissoes: totalVendas * 0.1, // 10% de comissão
    }
  } catch (error) {
    console.error("Erro ao buscar stats de cambistas:", error)
    return { ativos: 0, totalVendas: 0, vendasHoje: 0, totalComissoes: 0 }
  }
}

// ==================== FUNÇÕES PARA CAMPEONATOS ====================

export async function getCampeonatos() {
  try {
    const campeonatos = await executeQuery("SELECT * FROM campeonatos ORDER BY data_criacao DESC")
    return campeonatos || []
  } catch (error) {
    console.error("Erro ao buscar campeonatos:", error)
    return []
  }
}

export async function getCampeonatosStats() {
  try {
    const [ativos, jogosHoje, total] = await Promise.all([
      executeQuerySingle('SELECT COUNT(*) as count FROM campeonatos WHERE status = "ativo"').catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM jogos WHERE DATE(data_jogo) = CURDATE()').catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM campeonatos").catch(() => ({ count: 0 })),
    ])

    return {
      ativos: ativos?.count || 0,
      jogosHoje: jogosHoje?.count || 0,
      internacionais: Math.floor((total?.count || 0) / 2), // Estimativa
    }
  } catch (error) {
    console.error("Erro ao buscar stats de campeonatos:", error)
    return { ativos: 0, jogosHoje: 0, internacionais: 0 }
  }
}

// ==================== FUNÇÕES PARA BOLÕES ====================

export async function getBoloes() {
  try {
    const boloes = await executeQuery(`
      SELECT
        b.*,
        COALESCE(u.nome, 'Usuário não encontrado') as criado_por_nome
      FROM boloes b
      LEFT JOIN usuarios u ON b.criado_por = u.id
      ORDER BY b.data_criacao DESC
    `)

    return boloes || []
  } catch (error) {
    console.error("Erro ao buscar bolões:", error)
    return []
  }
}

export async function getBoloesStats() {
  try {
    const [ativos, participantes, faturamento, finalizandoHoje] = await Promise.all([
      executeQuerySingle('SELECT COUNT(*) as count FROM boloes WHERE status = "ativo"').catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(DISTINCT usuario_id) as count FROM apostas").catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COALESCE(SUM(valor_total), 0) as total FROM apostas WHERE status = "paga"')
        .catch(() => ({ total: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM boloes WHERE DATE(data_fim) = CURDATE()')
        .catch(() => ({ count: 0 })),
    ])

    return {
      ativos: ativos?.count || 0,
      participantes: participantes?.count || 0,
      faturamento: faturamento?.total || 0,
      finalizandoHoje: finalizandoHoje?.count || 0,
    }
  } catch (error) {
    console.error("Erro ao buscar stats de bolões:", error)
    return { ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 }
  }
}

export async function createBolao(bolaoData) {
  try {
    console.log("🔍 Dados recebidos para criar bolão:", bolaoData)

    const {
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes,
      min_acertos,
      data_inicio,
      data_fim,
      status,
      criado_por,
      regras,
      campeonatos_selecionados,
      partidas_selecionadas
    } = bolaoData

    // Preparar os parâmetros para a query
    const params = [
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes || 100,
      min_acertos || 3,
      data_inicio,
      data_fim,
      status || 'ativo',
      criado_por,
      regras ? JSON.stringify(regras) : null,
      campeonatos_selecionados ? JSON.stringify(campeonatos_selecionados) : null,
      partidas_selecionadas ? JSON.stringify(partidas_selecionadas) : null
    ]

    console.log("🔍 Parâmetros da query:", params)
    console.log("🔍 Número de parâmetros:", params.length)

    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por, regras,
        campeonatos_selecionados, partidas_selecionadas
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, params)

    const bolaoId = result.insertId
    console.log("✅ Bolão criado com ID:", bolaoId)

    // Associar partidas ao bolão na tabela bolao_jogos
    if (partidas_selecionadas && Array.isArray(partidas_selecionadas) && partidas_selecionadas.length > 0) {
      console.log("🔍 Associando partidas ao bolão:", partidas_selecionadas)

      for (const partida of partidas_selecionadas) {
        // Verificar se partida é um objeto ou apenas um ID
        const partidaId = typeof partida === 'object' ? partida.id : partida

        if (partidaId) {
          // Verificar se o jogo existe antes de tentar associar
          const jogoExiste = await executeQuerySingle('SELECT id FROM jogos WHERE id = ?', [partidaId])

          if (jogoExiste) {
            console.log("🔍 Inserindo partida ID:", partidaId)
            await executeQuery(`
              INSERT INTO bolao_jogos (bolao_id, jogo_id) VALUES (?, ?)
            `, [bolaoId, partidaId])
          } else {
            console.warn("⚠️ Jogo com ID", partidaId, "não encontrado na tabela jogos. Pulando...")
          }
        }
      }
    }

    return bolaoId
  } catch (error) {
    console.error("❌ Erro ao criar bolão:", error)
    console.error("❌ Stack trace:", error.stack)
    throw error
  }
}

// ==================== FUNÇÕES PARA JOGOS ====================

export async function getJogos(filters = {}) {
  try {
    let query = `
      SELECT
        j.*,
        c.nome as campeonato_nome,
        tc.nome as time_casa_nome,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tf.nome as time_fora_nome,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo
      FROM jogos j
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      WHERE 1=1
    `
    const params = []

    if (filters.campeonato_id) {
      query += " AND j.campeonato_id = ?"
      params.push(filters.campeonato_id)
    }

    if (filters.status) {
      query += " AND j.status = ?"
      params.push(filters.status)
    }

    if (filters.data_inicio && filters.data_fim) {
      query += " AND DATE(j.data_jogo) BETWEEN ? AND ?"
      params.push(filters.data_inicio, filters.data_fim)
    } else if (filters.data_inicio) {
      query += " AND DATE(j.data_jogo) >= ?"
      params.push(filters.data_inicio)
    } else if (filters.data_fim) {
      query += " AND DATE(j.data_jogo) <= ?"
      params.push(filters.data_fim)
    }

    query += " ORDER BY j.data_jogo ASC"

    if (filters.limit) {
      query += " LIMIT ?"
      params.push(parseInt(filters.limit))
    }

    const jogos = await executeQuery(query, params)
    return jogos || []
  } catch (error) {
    console.error("Erro ao buscar jogos:", error)
    return []
  }
}

export async function getJogosStats() {
  try {
    const [hoje, semana, total, aoVivo] = await Promise.all([
      executeQuerySingle('SELECT COUNT(*) as count FROM jogos WHERE DATE(data_jogo) = CURDATE()').catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM jogos WHERE data_jogo BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)').catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM jogos").catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM jogos WHERE status = "ao_vivo"').catch(() => ({ count: 0 })),
    ])

    return {
      hoje: hoje?.count || 0,
      semana: semana?.count || 0,
      total: total?.count || 0,
      aoVivo: aoVivo?.count || 0,
    }
  } catch (error) {
    console.error("Erro ao buscar stats de jogos:", error)
    return { hoje: 0, semana: 0, total: 0, aoVivo: 0 }
  }
}

// ==================== FUNÇÕES PARA DASHBOARD ====================

export async function getDashboardStats() {
  try {
    const [boloes, usuarios, faturamento, apostas, cambistas, jogosHoje] = await Promise.all([
      executeQuerySingle("SELECT COUNT(*) as count FROM boloes").catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE status = "ativo"').catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COALESCE(SUM(valor_total), 0) as total FROM apostas WHERE status = "paga" AND MONTH(data_aposta) = MONTH(CURDATE())').catch(() => ({ total: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM apostas WHERE DATE(data_aposta) = CURDATE()').catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE tipo = "cambista" AND status = "ativo"').catch(() => ({ count: 0 })),
      executeQuerySingle('SELECT COUNT(*) as count FROM jogos WHERE DATE(data_jogo) = CURDATE()').catch(() => ({ count: 0 })),
    ])

    return {
      totalBoloes: boloes?.count || 0,
      totalUsuarios: usuarios?.count || 0,
      faturamentoMes: faturamento?.total || 0,
      apostasHoje: apostas?.count || 0,
      cambistasAtivos: cambistas?.count || 0,
      jogosHoje: jogosHoje?.count || 0,
    }
  } catch (error) {
    console.error("Erro ao buscar stats do dashboard:", error)
    return {
      totalBoloes: 0,
      totalUsuarios: 0,
      faturamentoMes: 0,
      apostasHoje: 0,
      cambistasAtivos: 0,
      jogosHoje: 0,
    }
  }
}

// ==================== FUNÇÕES AUXILIARES ====================

export async function closeDatabase() {
  if (pool) {
    await pool.end()
    pool = null
  }
}
