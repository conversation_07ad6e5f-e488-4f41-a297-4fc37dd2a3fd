import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'cbeb9f19b15e4252b3f9d3375fefcfcc'

async function fetchFootballData(endpoint: string) {
  try {
    console.log(`🌐 Buscando: ${FOOTBALL_API_URL}${endpoint}`)

    const response = await fetch(`${FOOTBALL_API_URL}${endpoint}`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error)
    throw error
  }
}

export async function POST() {
  try {
    await initializeDatabase()

    // Verificar/criar tabela times se não existir
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS times (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome VARCHAR(255) NOT NULL,
        nome_curto VARCHAR(50),
        pais VARCHAR(100),
        logo_url TEXT,
        api_id VARCHAR(50),
        data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_api_id (api_id),
        INDEX idx_nome (nome)
      )
    `)

    // Verificar/criar tabela jogos se não existir
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS jogos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        campeonato_id INT NOT NULL,
        time_casa_id INT NOT NULL,
        time_fora_id INT NOT NULL,
        data_jogo DATETIME NOT NULL,
        rodada INT DEFAULT 1,
        status ENUM('agendado', 'ao_vivo', 'finalizado', 'adiado', 'cancelado') DEFAULT 'agendado',
        api_id VARCHAR(50),
        placar_casa INT DEFAULT NULL,
        placar_fora INT DEFAULT NULL,
        venue VARCHAR(255) DEFAULT NULL,
        data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_campeonato (campeonato_id),
        INDEX idx_data_jogo (data_jogo),
        INDEX idx_api_id (api_id),
        INDEX idx_status (status)
      )
    `)

    console.log("🔄 Iniciando sincronização automática de partidas...")

    // Buscar campeonatos ativos
    const campeonatos = await executeQuery(`
      SELECT id, codigo, nome FROM campeonatos 
      WHERE status = 'ativo' AND codigo IS NOT NULL
      ORDER BY id DESC
      LIMIT 12
    `)

    if (campeonatos.length === 0) {
      return NextResponse.json({
        success: false,
        error: "Nenhum campeonato encontrado",
        message: "Execute primeiro a sincronização de campeonatos"
      }, { status: 400 })
    }

    let syncedCount = 0
    let updatedCount = 0
    let errorCount = 0

    // Data de hoje e próximos 60 dias
    const dateFrom = new Date().toISOString().split('T')[0]
    const dateTo = new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

    for (const campeonato of campeonatos) {
      try {
        console.log(`🏆 Sincronizando partidas de ${campeonato.nome}...`)

        // Buscar partidas da competição
        const matchesData = await fetchFootballData(
          `/competitions/${campeonato.codigo}/matches?dateFrom=${dateFrom}&dateTo=${dateTo}&status=SCHEDULED,TIMED,IN_PLAY,PAUSED,FINISHED`
        )

        const matches = matchesData.matches || []
        console.log(`📅 ${matches.length} partidas encontradas para ${campeonato.nome}`)

        for (const match of matches) {
          try {
            // Verificar se a partida já existe
            const existing = await executeQuery(
              'SELECT id FROM jogos WHERE api_id = ?',
              [match.id]
            )

            // Buscar ou criar times
            const homeTeam = await getOrCreateTeam(match.homeTeam)
            const awayTeam = await getOrCreateTeam(match.awayTeam)

            if (!homeTeam || !awayTeam) {
              console.warn(`⚠️ Times não encontrados para partida ${match.id}`)
              continue
            }

            const matchData = {
              campeonato_id: campeonato.id,
              time_casa_id: homeTeam.id,
              time_fora_id: awayTeam.id,
              data_jogo: match.utcDate,
              rodada: match.matchday || 1,
              status: getMatchStatus(match.status),
              api_id: match.id,
              placar_casa: match.score?.fullTime?.home || null,
              placar_fora: match.score?.fullTime?.away || null,
              venue: match.venue || null
            }

            if (existing.length > 0) {
              // Atualizar existente
              await executeQuery(`
                UPDATE jogos SET
                  data_jogo = ?,
                  rodada = ?,
                  status = ?,
                  placar_casa = ?,
                  placar_fora = ?,
                  venue = ?,
                  data_atualizacao = NOW()
                WHERE id = ?
              `, [
                matchData.data_jogo,
                matchData.rodada,
                matchData.status,
                matchData.placar_casa,
                matchData.placar_fora,
                matchData.venue,
                existing[0].id
              ])
              
              updatedCount++
            } else {
              // Criar novo
              await executeQuery(`
                INSERT INTO jogos (
                  campeonato_id, time_casa_id, time_fora_id, data_jogo,
                  rodada, status, api_id, placar_casa, placar_fora,
                  venue, data_criacao
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
              `, [
                matchData.campeonato_id,
                matchData.time_casa_id,
                matchData.time_fora_id,
                matchData.data_jogo,
                matchData.rodada,
                matchData.status,
                matchData.api_id,
                matchData.placar_casa,
                matchData.placar_fora,
                matchData.venue
              ])
              
              syncedCount++
            }

          } catch (matchError) {
            console.error(`❌ Erro ao processar partida ${match.id}:`, matchError)
            errorCount++
          }
        }

        // Delay para evitar rate limit
        await new Promise(resolve => setTimeout(resolve, 200))

      } catch (compError) {
        console.error(`❌ Erro ao sincronizar ${campeonato.nome}:`, compError)
        errorCount++
      }
    }

    console.log(`✅ Sincronização concluída: ${syncedCount} criadas, ${updatedCount} atualizadas, ${errorCount} erros`)

    return NextResponse.json({
      success: true,
      message: "Sincronização de partidas concluída",
      data: {
        synced: syncedCount,
        updated: updatedCount,
        errors: errorCount,
        competitions_processed: campeonatos.length
      }
    })

  } catch (error) {
    console.error("❌ Erro na sincronização:", error)
    return NextResponse.json({
      success: false,
      error: "Erro na sincronização de partidas",
      message: error.message
    }, { status: 500 })
  }
}

async function getOrCreateTeam(teamData: any) {
  if (!teamData || !teamData.name) return null

  try {
    // Verificar se o time já existe
    let existing = await executeQuery(
      'SELECT id FROM times WHERE api_id = ? OR nome = ?',
      [teamData.id, teamData.name]
    )

    if (existing.length > 0) {
      return existing[0]
    }

    // Criar novo time
    const result = await executeQuery(`
      INSERT INTO times (
        nome, nome_curto, pais, logo_url, api_id, data_criacao
      ) VALUES (?, ?, ?, ?, ?, NOW())
    `, [
      teamData.name,
      teamData.shortName || teamData.tla || teamData.name.substring(0, 3).toUpperCase(),
      teamData.area?.name || 'Internacional',
      teamData.crest,
      teamData.id
    ])

    return { id: result.insertId }

  } catch (error) {
    console.error(`❌ Erro ao criar/buscar time ${teamData.name}:`, error)
    return null
  }
}

function getMatchStatus(apiStatus: string): string {
  switch (apiStatus) {
    case 'SCHEDULED':
    case 'TIMED':
      return 'agendado'
    case 'IN_PLAY':
    case 'PAUSED':
      return 'ao_vivo'
    case 'FINISHED':
      return 'finalizado'
    case 'POSTPONED':
      return 'adiado'
    case 'CANCELLED':
      return 'cancelado'
    default:
      return 'agendado'
  }
}
