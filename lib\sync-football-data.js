import { executeQuery, executeQuerySingle, initializeDatabase } from './database.js'

// Configuração da API Football-data.org
const FOOTBALL_API_KEY = process.env.FOOTBALL_API_KEY || 'YOUR_API_KEY_HERE'
const FOOTBALL_API_BASE = 'https://api.football-data.org/v4'

const headers = {
  'X-Auth-Token': FOOTBALL_API_KEY,
  'Content-Type': 'application/json'
}

// Função para fazer requisições à API
async function fetchFromAPI(endpoint) {
  try {
    const response = await fetch(`${FOOTBALL_API_BASE}${endpoint}`, { headers })
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} - ${response.statusText}`)
    }
    
    return await response.json()
  } catch (error) {
    console.error(`❌ Erro ao buscar dados da API: ${endpoint}`, error)
    throw error
  }
}

// Sincronizar competições/campeonatos
async function syncCompetitions() {
  try {
    console.log('🔄 Sincronizando competições...')
    
    const data = await fetchFromAPI('/competitions')
    
    for (const competition of data.competitions) {
      // Verificar se a competição já existe
      const existing = await executeQuerySingle(
        'SELECT id FROM campeonatos WHERE external_id = ?',
        [competition.id]
      )
      
      if (!existing) {
        // Inserir nova competição
        await executeQuery(`
          INSERT INTO campeonatos (
            external_id, nome, descricao, pais, logo_url, status, data_criacao
          ) VALUES (?, ?, ?, ?, ?, 'ativo', NOW())
        `, [
          competition.id,
          competition.name,
          competition.name,
          competition.area?.name || 'Internacional',
          competition.emblem || null
        ])
        
        console.log(`✅ Competição adicionada: ${competition.name}`)
      } else {
        // Atualizar competição existente
        await executeQuery(`
          UPDATE campeonatos SET
            nome = ?, descricao = ?, pais = ?, logo_url = ?, data_atualizacao = NOW()
          WHERE external_id = ?
        `, [
          competition.name,
          competition.name,
          competition.area?.name || 'Internacional',
          competition.emblem || null,
          competition.id
        ])
      }
    }
    
    console.log('✅ Sincronização de competições concluída')
  } catch (error) {
    console.error('❌ Erro ao sincronizar competições:', error)
  }
}

// Sincronizar times
async function syncTeams() {
  try {
    console.log('🔄 Sincronizando times...')
    
    // Buscar competições ativas
    const competitions = await executeQuery('SELECT external_id FROM campeonatos WHERE status = "ativo"')
    
    for (const competition of competitions.slice(0, 5)) { // Limitar para evitar rate limit
      try {
        const data = await fetchFromAPI(`/competitions/${competition.external_id}/teams`)
        
        for (const team of data.teams) {
          // Verificar se o time já existe
          const existing = await executeQuerySingle(
            'SELECT id FROM times WHERE external_id = ?',
            [team.id]
          )
          
          if (!existing) {
            // Inserir novo time
            await executeQuery(`
              INSERT INTO times (
                external_id, nome, nome_curto, logo_url, pais, data_criacao
              ) VALUES (?, ?, ?, ?, ?, NOW())
            `, [
              team.id,
              team.name,
              team.shortName || team.tla || team.name.substring(0, 3).toUpperCase(),
              team.crest || null,
              team.area?.name || 'Desconhecido'
            ])
            
            console.log(`✅ Time adicionado: ${team.name}`)
          } else {
            // Atualizar time existente
            await executeQuery(`
              UPDATE times SET
                nome = ?, nome_curto = ?, logo_url = ?, pais = ?, data_atualizacao = NOW()
              WHERE external_id = ?
            `, [
              team.name,
              team.shortName || team.tla || team.name.substring(0, 3).toUpperCase(),
              team.crest || null,
              team.area?.name || 'Desconhecido',
              team.id
            ])
          }
        }
        
        // Delay para evitar rate limit
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.error(`❌ Erro ao sincronizar times da competição ${competition.external_id}:`, error)
      }
    }
    
    console.log('✅ Sincronização de times concluída')
  } catch (error) {
    console.error('❌ Erro ao sincronizar times:', error)
  }
}

// Sincronizar partidas/jogos
async function syncMatches() {
  try {
    console.log('🔄 Sincronizando partidas...')
    
    // Buscar competições ativas
    const competitions = await executeQuery('SELECT id, external_id FROM campeonatos WHERE status = "ativo"')
    
    for (const competition of competitions.slice(0, 5)) { // Limitar para evitar rate limit
      try {
        const data = await fetchFromAPI(`/competitions/${competition.external_id}/matches`)
        
        for (const match of data.matches) {
          // Buscar IDs dos times no banco local
          const homeTeam = await executeQuerySingle(
            'SELECT id FROM times WHERE external_id = ?',
            [match.homeTeam.id]
          )
          
          const awayTeam = await executeQuerySingle(
            'SELECT id FROM times WHERE external_id = ?',
            [match.awayTeam.id]
          )
          
          if (!homeTeam || !awayTeam) {
            console.warn(`⚠️ Times não encontrados para partida ${match.id}`)
            continue
          }
          
          // Verificar se a partida já existe
          const existing = await executeQuerySingle(
            'SELECT id FROM jogos WHERE external_id = ?',
            [match.id]
          )
          
          const status = match.status === 'SCHEDULED' ? 'agendado' :
                        match.status === 'LIVE' ? 'ao_vivo' :
                        match.status === 'FINISHED' ? 'finalizado' : 'agendado'
          
          if (!existing) {
            // Inserir nova partida
            await executeQuery(`
              INSERT INTO jogos (
                external_id, campeonato_id, time_casa_id, time_fora_id,
                data_jogo, status, placar_casa, placar_fora, data_criacao
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `, [
              match.id,
              competition.id,
              homeTeam.id,
              awayTeam.id,
              match.utcDate,
              status,
              match.score?.fullTime?.home || null,
              match.score?.fullTime?.away || null
            ])
            
            console.log(`✅ Partida adicionada: ${match.homeTeam.shortName} vs ${match.awayTeam.shortName}`)
          } else {
            // Atualizar partida existente
            await executeQuery(`
              UPDATE jogos SET
                data_jogo = ?, status = ?, placar_casa = ?, placar_fora = ?, data_atualizacao = NOW()
              WHERE external_id = ?
            `, [
              match.utcDate,
              status,
              match.score?.fullTime?.home || null,
              match.score?.fullTime?.away || null,
              match.id
            ])
          }
        }
        
        // Delay para evitar rate limit
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.error(`❌ Erro ao sincronizar partidas da competição ${competition.external_id}:`, error)
      }
    }
    
    console.log('✅ Sincronização de partidas concluída')
  } catch (error) {
    console.error('❌ Erro ao sincronizar partidas:', error)
  }
}

// Função principal de sincronização
export async function syncFootballData() {
  try {
    console.log('🚀 Iniciando sincronização com Football-data.org...')
    
    await initializeDatabase()
    
    // Sincronizar em ordem: competições -> times -> partidas
    await syncCompetitions()
    await syncTeams()
    await syncMatches()
    
    console.log('✅ Sincronização completa finalizada!')
    
    // Atualizar timestamp da última sincronização
    await executeQuery(`
      INSERT INTO configuracoes (chave, valor, data_atualizacao)
      VALUES ('ultima_sincronizacao', NOW(), NOW())
      ON DUPLICATE KEY UPDATE valor = NOW(), data_atualizacao = NOW()
    `)
    
  } catch (error) {
    console.error('❌ Erro na sincronização:', error)
  }
}

// Configurar sincronização automática de hora em hora
export function startAutoSync() {
  console.log('⏰ Configurando sincronização automática de hora em hora...')
  
  // Executar imediatamente
  syncFootballData()
  
  // Configurar para executar a cada hora (3600000 ms)
  setInterval(() => {
    console.log('⏰ Executando sincronização automática...')
    syncFootballData()
  }, 3600000) // 1 hora
  
  console.log('✅ Sincronização automática configurada!')
}
