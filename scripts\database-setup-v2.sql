-- Script para popular o banco com dados de exemplo

-- Inserir mais usuários de exemplo
INSERT OR IGNORE INTO usuarios (nome, email, telefone, senha_hash, tipo, status) VALUES
('<PERSON>', '<EMAIL>', '(11) 99999-9999', '$2b$10$hash_exemplo', 'usuario', 'ativo'),
('<PERSON>', '<EMAIL>', '(11) 88888-8888', '$2b$10$hash_exemplo', 'cambista', 'ativo'),
('<PERSON>', '<EMAIL>', '(11) 77777-7777', '$2b$10$hash_exemplo', 'usuario', 'inativo'),
('<PERSON>', '<EMAIL>', '(11) 66666-6666', '$2b$10$hash_exemplo', 'cambista', 'ativo');

-- Inserir mais campeonatos
INSERT OR IGNORE INTO campeonatos (nome, descricao, pais, temporada, status) VALUES
('Premier League', 'Campeonato Inglês', 'Inglaterra', '2024/25', 'ativo'),
('La Liga', 'Campeonato Espanhol', 'Espanha', '2024/25', 'ativo'),
('Serie A', 'Campeonato Italiano', 'Itália', '2024/25', 'ativo'),
('Bundesliga', 'Campeonato Alemão', 'Alemanha', '2024/25', 'ativo');

-- Inserir alguns times de exemplo
INSERT OR IGNORE INTO times (nome, nome_curto, campeonato_id) VALUES
('Flamengo', 'FLA', 1),
('Palmeiras', 'PAL', 1),
('São Paulo', 'SAO', 1),
('Corinthians', 'COR', 1);

-- Inserir alguns jogos de exemplo
INSERT OR IGNORE INTO jogos (campeonato_id, time_casa_id, time_visitante_id, data_jogo, status) VALUES
(1, 1, 2, datetime('now', '+1 day'), 'agendado'),
(1, 3, 4, datetime('now', '+2 days'), 'agendado'),
(1, 1, 3, datetime('now', '+3 days'), 'agendado');

-- Inserir um bolão de exemplo
INSERT OR IGNORE INTO boloes (nome, descricao, valor_aposta, premio_total, max_participantes, status, data_inicio, data_fim, criado_por) VALUES
('Bolão do Brasileirão', 'Bolão para o Campeonato Brasileiro 2024', 10.00, 1000.00, 100, 'ativo', datetime('now'), datetime('now', '+30 days'), 1);

-- Inserir algumas apostas de exemplo
INSERT OR IGNORE INTO apostas (usuario_id, bolao_id, jogo_id, palpite_casa, palpite_visitante, valor_aposta, valor_total, status) VALUES
(2, 1, 1, 2, 1, 10.00, 10.00, 'paga'),
(3, 1, 1, 1, 1, 10.00, 10.00, 'paga'),
(4, 1, 2, 3, 0, 10.00, 10.00, 'paga');
