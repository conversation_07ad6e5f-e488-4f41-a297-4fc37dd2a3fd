import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Buscando bolões reais...')

    // Buscar bolões reais de forma simples
    const boloes = await executeQuery(`
      SELECT * FROM boloes
      WHERE status IN ('ativo', 'em_breve')
      ORDER BY id DESC
      LIMIT 10
    `)

    console.log(`📊 Bolões encontrados: ${boloes.length}`)

    // Formatar bolões e buscar jogos
    const boloesFormatados = await Promise.all(boloes.map(async (bolao: any) => {
      let campeonatos = []
      let jogos = []

      // Tentar parsear campeonatos selecionados
      if (bolao.campeonatos_selecionados) {
        try {
          campeonatos = JSON.parse(bolao.campeonatos_selecionados)
        } catch (e) {
          console.warn(`⚠️ Erro ao parsear campeonatos do bolão ${bolao.id}`)
        }
      }

      // Buscar jogos do bolão
      try {
        // Primeiro tentar buscar da tabela bolao_jogos
        const jogosAssociados = await executeQuery(`
          SELECT
            j.*,
            tc.nome as time_casa_nome,
            tc.nome_curto as time_casa_curto,
            tc.logo_url as time_casa_logo,
            tf.nome as time_fora_nome,
            tf.nome_curto as time_fora_curto,
            tf.logo_url as time_fora_logo,
            c.nome as campeonato_nome,
            c.codigo as campeonato_codigo
          FROM bolao_jogos bj
          JOIN jogos j ON bj.jogo_id = j.id
          LEFT JOIN times tc ON j.time_casa_id = tc.id
          LEFT JOIN times tf ON j.time_fora_id = tf.id
          LEFT JOIN campeonatos c ON j.campeonato_id = c.id
          WHERE bj.bolao_id = ?
          ORDER BY j.data_jogo ASC
        `, [bolao.id])

        // Se não há jogos associados, tentar buscar das partidas selecionadas
        if (jogosAssociados.length === 0 && bolao.partidas_selecionadas) {
          try {
            const partidasSelecionadas = JSON.parse(bolao.partidas_selecionadas)
            if (partidasSelecionadas && partidasSelecionadas.length > 0) {
              console.log(`🔍 Buscando ${partidasSelecionadas.length} partidas selecionadas para bolão ${bolao.id}`)

              // Buscar jogos das partidas selecionadas
              const partidasIds = partidasSelecionadas.map((p: any) => p.id || p).filter(Boolean)
              if (partidasIds.length > 0) {
                const placeholders = partidasIds.map(() => '?').join(',')
                const jogosPartidas = await executeQuery(`
                  SELECT
                    j.*,
                    tc.nome as time_casa_nome,
                    tc.nome_curto as time_casa_curto,
                    tc.logo_url as time_casa_logo,
                    tf.nome as time_fora_nome,
                    tf.nome_curto as time_fora_curto,
                    tf.logo_url as time_fora_logo,
                    c.nome as campeonato_nome,
                    c.codigo as campeonato_codigo
                  FROM jogos j
                  LEFT JOIN times tc ON j.time_casa_id = tc.id
                  LEFT JOIN times tf ON j.time_fora_id = tf.id
                  LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                  WHERE j.id IN (${placeholders})
                  ORDER BY j.data_jogo ASC
                `, partidasIds)

                jogos = jogosPartidas
              }
            }
          } catch (e) {
            console.warn(`⚠️ Erro ao parsear partidas selecionadas do bolão ${bolao.id}`)
          }
        }
        // Se ainda não há jogos e há campeonatos selecionados, buscar jogos desses campeonatos
        else if (jogosAssociados.length === 0 && campeonatos.length > 0) {
          console.log(`🔍 Buscando jogos de TODOS os campeonatos selecionados para bolão ${bolao.id}`)

          // Buscar jogos de TODOS os campeonatos selecionados (MÁXIMO 11 TOTAL)
          const codigosCampeonatos = campeonatos.map((c: any) => c.codigo).filter(Boolean)
          if (codigosCampeonatos.length > 0) {
            console.log(`🏆 Campeonatos: ${codigosCampeonatos.join(', ')}`)

            // Calcular quantos jogos por campeonato (distribuição equilibrada)
            const jogosPorCampeonato = Math.ceil(11 / codigosCampeonatos.length)
            console.log(`📊 Distribuição: ${jogosPorCampeonato} jogos por campeonato (máx 11 total)`)

            // Buscar jogos de cada campeonato separadamente para garantir representação de todos
            let todosJogos = []
            for (const codigo of codigosCampeonatos) {
              const jogosCampeonato = await executeQuery(`
                SELECT
                  j.*,
                  tc.nome as time_casa_nome,
                  tc.nome_curto as time_casa_curto,
                  tc.logo_url as time_casa_logo,
                  tf.nome as time_fora_nome,
                  tf.nome_curto as time_fora_curto,
                  tf.logo_url as time_fora_logo,
                  c.nome as campeonato_nome,
                  c.codigo as campeonato_codigo
                FROM jogos j
                LEFT JOIN times tc ON j.time_casa_id = tc.id
                LEFT JOIN times tf ON j.time_fora_id = tf.id
                LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                WHERE c.codigo = ?
                AND j.status IN ('agendado', 'ao_vivo')
                AND j.data_jogo >= NOW()
                ORDER BY j.data_jogo ASC
                LIMIT ?
              `, [codigo, jogosPorCampeonato])

              console.log(`📊 ${codigo}: ${jogosCampeonato.length} jogos encontrados`)
              todosJogos = [...todosJogos, ...jogosCampeonato]
            }

            // Ordenar todos os jogos por data
            jogos = todosJogos.sort((a, b) => new Date(a.data_jogo).getTime() - new Date(b.data_jogo).getTime())

            // Se não temos 11 jogos, buscar mais de qualquer campeonato
            if (jogos.length < 11) {
              console.log(`⚠️ Apenas ${jogos.length} jogos encontrados, buscando mais...`)

              const jogosAdicionais = await executeQuery(`
                SELECT
                  j.*,
                  tc.nome as time_casa_nome,
                  tc.nome_curto as time_casa_curto,
                  tc.logo_url as time_casa_logo,
                  tf.nome as time_fora_nome,
                  tf.nome_curto as time_fora_curto,
                  tf.logo_url as time_fora_logo,
                  c.nome as campeonato_nome,
                  c.codigo as campeonato_codigo
                FROM jogos j
                LEFT JOIN times tc ON j.time_casa_id = tc.id
                LEFT JOIN times tf ON j.time_fora_id = tf.id
                LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                WHERE j.status IN ('agendado', 'ao_vivo')
                AND j.data_jogo >= NOW()
                ORDER BY j.data_jogo ASC
                LIMIT ?
              `, [11])

              // Combinar e remover duplicatas
              const jogosExistentesIds = new Set(jogos.map(j => j.id))
              const novosjogos = jogosAdicionais.filter(j => !jogosExistentesIds.has(j.id))

              jogos = [...jogos, ...novosjogos]
                .sort((a, b) => new Date(a.data_jogo).getTime() - new Date(b.data_jogo).getTime())
                .slice(0, 11)

              console.log(`✅ Total final de jogos: ${jogos.length}`)
            } else {
              // Limitar a 11 se temos mais
              jogos = jogos.slice(0, 11)
              console.log(`✅ Total de jogos carregados: ${jogos.length} (limitado a 11)`)
            }
          }
        } else {
          jogos = jogosAssociados
        }

      } catch (error) {
        console.error(`❌ Erro ao buscar jogos do bolão ${bolao.id}:`, error)
      }

      return {
        id: bolao.id,
        nome: bolao.nome || 'Bolão',
        descricao: bolao.descricao || 'Descrição do bolão',
        valor_aposta: parseFloat(bolao.valor_aposta || 25),
        premio_total: parseFloat(bolao.premio_total || 1000),
        data_inicio: bolao.data_inicio,
        data_fim: bolao.data_fim,
        status: bolao.status,
        participantes: 0,
        max_participantes: bolao.max_participantes || 100,
        total_jogos: jogos.length,
        criador: 'Admin',
        banner_image: bolao.banner_image,
        campeonatos_selecionados: campeonatos,
        jogos: jogos.map((jogo: any) => ({
          id: jogo.id,
          time_casa: jogo.time_casa_nome || jogo.time_casa,
          time_fora: jogo.time_fora_nome || jogo.time_fora,
          time_casa_logo: jogo.time_casa_logo,
          time_fora_logo: jogo.time_fora_logo,
          data_jogo: jogo.data_jogo,
          campeonato: jogo.campeonato_nome,
          campeonato_codigo: jogo.campeonato_codigo,
          status: jogo.status,
          resultado_casa: jogo.resultado_casa,
          resultado_fora: jogo.resultado_fora
        }))
      }
    }))

    console.log(`✅ Retornando ${boloesFormatados.length} bolões reais`)

    return NextResponse.json({
      success: true,
      boloes: boloesFormatados
    })

  } catch (error) {
    console.error('❌ Erro ao buscar bolões:', error.message)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message,
      boloes: []
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes,
      min_acertos,
      data_inicio,
      data_fim,
      jogos_selecionados,
      regras
    } = body

    if (!nome || !valor_aposta || !premio_total || !data_inicio || !data_fim) {
      return NextResponse.json(
        { error: 'Dados obrigatórios não fornecidos' },
        { status: 400 }
      )
    }

    // Por enquanto, vamos usar um usuário padrão como criador
    // Em produção, isso deveria vir da sessão do usuário
    const criado_por = 1

    // Inserir bolão
    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por, regras
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'em_breve', ?, ?)
    `, [
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes || null,
      min_acertos || 3,
      data_inicio,
      data_fim,
      criado_por,
      JSON.stringify(regras || [])
    ])

    const bolao_id = (result as any).insertId

    // Inserir jogos do bolão se fornecidos
    if (jogos_selecionados && jogos_selecionados.length > 0) {
      for (const jogo_id of jogos_selecionados) {
        await executeQuery(`
          INSERT INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolao_id, jogo_id])
      }
    }

    return NextResponse.json({
      success: true,
      bolao_id,
      message: 'Bolão criado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao criar bolão:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
