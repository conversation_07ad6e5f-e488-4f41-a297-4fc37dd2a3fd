import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Buscando bolões reais...')

    // Buscar bolões reais de forma simples
    const boloes = await executeQuery(`
      SELECT * FROM boloes
      WHERE status IN ('ativo', 'em_breve')
      ORDER BY id DESC
      LIMIT 10
    `)

    console.log(`📊 Bolões encontrados: ${boloes.length}`)

    // Formatar bolões
    const boloesFormatados = boloes.map((bolao: any) => {
      let campeonatos = []

      // Tentar parsear campeonatos selecionados
      if (bolao.campeonatos_selecionados) {
        try {
          campeonatos = JSON.parse(bolao.campeonatos_selecionados)
        } catch (e) {
          console.warn(`⚠️ Erro ao parsear campeonatos do bolão ${bolao.id}`)
        }
      }

      return {
        id: bolao.id,
        nome: bolao.nome || 'Bolão',
        descricao: bolao.descricao || 'Descrição do bolão',
        valor_aposta: parseFloat(bolao.valor_aposta || 25),
        premio_total: parseFloat(bolao.premio_total || 1000),
        data_inicio: bolao.data_inicio,
        data_fim: bolao.data_fim,
        status: bolao.status,
        participantes: 0,
        max_participantes: bolao.max_participantes || 100,
        total_jogos: 0,
        criador: 'Admin',
        banner_image: bolao.banner_image,
        campeonatos_selecionados: campeonatos,
        jogos: []
      }
    })

    console.log(`✅ Retornando ${boloesFormatados.length} bolões reais`)

    return NextResponse.json({
      success: true,
      boloes: boloesFormatados
    })

  } catch (error) {
    console.error('❌ Erro ao buscar bolões:', error.message)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message,
      boloes: []
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes,
      min_acertos,
      data_inicio,
      data_fim,
      jogos_selecionados,
      regras
    } = body

    if (!nome || !valor_aposta || !premio_total || !data_inicio || !data_fim) {
      return NextResponse.json(
        { error: 'Dados obrigatórios não fornecidos' },
        { status: 400 }
      )
    }

    // Por enquanto, vamos usar um usuário padrão como criador
    // Em produção, isso deveria vir da sessão do usuário
    const criado_por = 1

    // Inserir bolão
    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por, regras
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'em_breve', ?, ?)
    `, [
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes || null,
      min_acertos || 3,
      data_inicio,
      data_fim,
      criado_por,
      JSON.stringify(regras || [])
    ])

    const bolao_id = (result as any).insertId

    // Inserir jogos do bolão se fornecidos
    if (jogos_selecionados && jogos_selecionados.length > 0) {
      for (const jogo_id of jogos_selecionados) {
        await executeQuery(`
          INSERT INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolao_id, jogo_id])
      }
    }

    return NextResponse.json({
      success: true,
      bolao_id,
      message: 'Bolão criado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao criar bolão:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
