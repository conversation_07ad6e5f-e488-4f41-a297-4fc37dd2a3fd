import mysql from "mysql2/promise"

async function checkTableStructures() {
  try {
    console.log('🔍 Verificando estruturas das tabelas...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })

    // Verificar estrutura da tabela usuarios
    console.log('👥 Estrutura da tabela usuarios:')
    const [usuariosColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM information_schema.columns 
      WHERE table_schema = 'sistema-bolao-top' AND table_name = 'usuarios'
      ORDER BY ORDINAL_POSITION
    `)

    usuariosColumns.forEach((col, index) => {
      console.log(`  ${index + 1}. ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE} - Default: ${col.COLUMN_DEFAULT}`)
    })

    // Verificar se há usuários
    const [usuarios] = await connection.execute(`SELECT * FROM usuarios LIMIT 5`)
    console.log(`\n📊 Total de usuários: ${usuarios.length}`)
    if (usuarios.length > 0) {
      console.log('Primeiros usuários:')
      usuarios.forEach((user, index) => {
        console.log(`  ${index + 1}. ${JSON.stringify(user)}`)
      })
    }

    // Verificar estrutura da tabela bilhetes
    console.log('\n🎫 Estrutura da tabela bilhetes:')
    const [bilhetesColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM information_schema.columns 
      WHERE table_schema = 'sistema-bolao-top' AND table_name = 'bilhetes'
      ORDER BY ORDINAL_POSITION
    `)

    bilhetesColumns.forEach((col, index) => {
      console.log(`  ${index + 1}. ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE} - Default: ${col.COLUMN_DEFAULT}`)
    })

    // Verificar foreign keys
    console.log('\n🔗 Foreign keys da tabela bilhetes:')
    const [foreignKeys] = await connection.execute(`
      SELECT 
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM information_schema.KEY_COLUMN_USAGE
      WHERE table_schema = 'sistema-bolao-top' 
      AND table_name = 'bilhetes'
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `)

    foreignKeys.forEach((fk, index) => {
      console.log(`  ${index + 1}. ${fk.COLUMN_NAME} -> ${fk.REFERENCED_TABLE_NAME}.${fk.REFERENCED_COLUMN_NAME}`)
    })

    // Se não há usuários, criar um usuário simples
    if (usuarios.length === 0) {
      console.log('\n👤 Criando usuário de teste...')
      
      // Descobrir quais colunas são obrigatórias
      const requiredColumns = usuariosColumns.filter(col => col.IS_NULLABLE === 'NO' && col.COLUMN_NAME !== 'id')
      console.log('Colunas obrigatórias:', requiredColumns.map(col => col.COLUMN_NAME))
      
      // Criar usuário com dados mínimos
      try {
        const [insertUser] = await connection.execute(`
          INSERT INTO usuarios (nome, email, senha, created_at, updated_at)
          VALUES (?, ?, ?, NOW(), NOW())
        `, ['Usuário Teste', '<EMAIL>', 'senha123'])
        
        console.log(`✅ Usuário criado com ID: ${insertUser.insertId}`)
        
        // Agora inserir bilhetes de teste
        console.log('\n💾 Inserindo bilhetes de teste...')
        
        const bilhetes = [
          {
            codigo: 'BLT17526411592332151YS3W',
            transaction_id: 'pixi_01k08rvf1wfk9bg8z3t0zq1at9',
            valor_total: 0.60
          },
          {
            codigo: 'BLT175264119146122Z6GZDN',
            transaction_id: 'pixi_01k08rwejvf46s72a6bjk246fp',
            valor_total: 0.60
          }
        ]

        for (const bilhete of bilhetes) {
          try {
            const [insertBilhete] = await connection.execute(`
              INSERT INTO bilhetes (
                codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
                valor_total, quantidade_apostas, status, transaction_id,
                created_at, updated_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            `, [
              bilhete.codigo,
              insertUser.insertId,
              'Usuário Teste',
              '<EMAIL>',
              '000.000.000-00',
              bilhete.valor_total,
              11,
              'pendente',
              bilhete.transaction_id
            ])

            console.log(`✅ Bilhete ${bilhete.codigo} inserido com ID: ${insertBilhete.insertId}`)
            
          } catch (bilheteError) {
            console.error(`❌ Erro ao inserir bilhete ${bilhete.codigo}:`, bilheteError.message)
          }
        }
        
      } catch (userError) {
        console.error('❌ Erro ao criar usuário:', userError.message)
      }
    }

    // Verificar bilhetes finais
    console.log('\n📊 Bilhetes no banco:')
    const [bilhetesFinal] = await connection.execute(`
      SELECT id, codigo, transaction_id, status, valor_total FROM bilhetes
    `)

    console.log(`Total: ${bilhetesFinal.length}`)
    bilhetesFinal.forEach((bilhete, index) => {
      console.log(`  ${index + 1}. ${bilhete.codigo} - ${bilhete.transaction_id} - ${bilhete.status}`)
    })

    await connection.end()

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
  }
}

checkTableStructures()
