import mysql from "mysql2/promise"

async function createBolaoMultiplosCampeonatos() {
  try {
    console.log('🎯 Criando bolão com múltiplos campeonatos...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })

    // Primeiro, vamos criar alguns jogos para outros campeonatos
    console.log('⚽ Criando jogos para outros campeonatos...')
    
    // Buscar IDs dos campeonatos
    const [campeonatos] = await connection.execute(`
      SELECT id, nome FROM campeonatos 
      WHERE nome IN ('Premier League', 'Ligue 1', 'European Championship')
    `)
    
    console.log(`🏆 Campeonatos encontrados: ${campeonatos.length}`)
    campeonatos.forEach(c => console.log(`  - ${c.nome} (ID: ${c.id})`))
    
    // Buscar alguns times para criar jogos fict<PERSON>
    const [times] = await connection.execute(`
      SELECT id, nome, nome_curto FROM times LIMIT 20
    `)
    
    console.log(`👥 Times disponíveis: ${times.length}`)
    
    // Criar jogos para Premier League
    const premierLeague = campeonatos.find(c => c.nome === 'Premier League')
    if (premierLeague && times.length >= 10) {
      console.log(`\n⚽ Criando jogos para Premier League...`)
      
      for (let i = 0; i < 5; i++) {
        const timeCasa = times[i * 2]
        const timeFora = times[i * 2 + 1]
        const dataJogo = new Date()
        dataJogo.setDate(dataJogo.getDate() + i + 1)
        
        try {
          await connection.execute(`
            INSERT INTO jogos (time_casa_id, time_fora_id, campeonato_id, data_jogo, status)
            VALUES (?, ?, ?, ?, 'agendado')
          `, [timeCasa.id, timeFora.id, premierLeague.id, dataJogo.toISOString()])
          
          console.log(`    ✅ ${timeCasa.nome} vs ${timeFora.nome}`)
        } catch (error) {
          if (!error.message.includes('Duplicate entry')) {
            console.log(`    ❌ Erro: ${error.message}`)
          }
        }
      }
    }
    
    // Criar jogos para Ligue 1
    const ligue1 = campeonatos.find(c => c.nome === 'Ligue 1')
    if (ligue1 && times.length >= 15) {
      console.log(`\n⚽ Criando jogos para Ligue 1...`)
      
      for (let i = 5; i < 10; i++) {
        const timeCasa = times[i * 2]
        const timeFora = times[i * 2 + 1]
        const dataJogo = new Date()
        dataJogo.setDate(dataJogo.getDate() + i + 2)
        
        try {
          await connection.execute(`
            INSERT INTO jogos (time_casa_id, time_fora_id, campeonato_id, data_jogo, status)
            VALUES (?, ?, ?, ?, 'agendado')
          `, [timeCasa.id, timeFora.id, ligue1.id, dataJogo.toISOString()])
          
          console.log(`    ✅ ${timeCasa.nome} vs ${timeFora.nome}`)
        } catch (error) {
          if (!error.message.includes('Duplicate entry')) {
            console.log(`    ❌ Erro: ${error.message}`)
          }
        }
      }
    }
    
    // Criar jogos para European Championship
    const euroChampionship = campeonatos.find(c => c.nome === 'European Championship')
    if (euroChampionship && times.length >= 20) {
      console.log(`\n⚽ Criando jogos para European Championship...`)
      
      for (let i = 10; i < 15; i++) {
        const timeCasa = times[i]
        const timeFora = times[i + 5]
        const dataJogo = new Date()
        dataJogo.setDate(dataJogo.getDate() + i + 3)
        
        try {
          await connection.execute(`
            INSERT INTO jogos (time_casa_id, time_fora_id, campeonato_id, data_jogo, status)
            VALUES (?, ?, ?, ?, 'agendado')
          `, [timeCasa.id, timeFora.id, euroChampionship.id, dataJogo.toISOString()])
          
          console.log(`    ✅ ${timeCasa.nome} vs ${timeFora.nome}`)
        } catch (error) {
          if (!error.message.includes('Duplicate entry')) {
            console.log(`    ❌ Erro: ${error.message}`)
          }
        }
      }
    }
    
    // Agora criar o bolão
    console.log(`\n🎫 Criando bolão com múltiplos campeonatos...`)
    
    const [bolaoResult] = await connection.execute(`
      INSERT INTO boloes (
        nome, 
        descricao, 
        valor_aposta, 
        premio_total, 
        data_inicio, 
        data_fim, 
        status, 
        max_participantes,
        banner_image,
        criado_por
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'Bolão Multi-Campeonatos',
      'Bolão com jogos de múltiplos campeonatos: Brasileirão, Premier League, Ligue 1 e European Championship',
      25.00,
      10000.00,
      new Date().toISOString(),
      new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 dias
      'ativo',
      1000,
      '/placeholder.svg?height=200&width=400',
      1 // Assumindo que existe usuário com ID 1
    ])
    
    const bolaoId = bolaoResult.insertId
    console.log(`✅ Bolão criado com ID: ${bolaoId}`)
    
    // Associar jogos de múltiplos campeonatos ao bolão
    console.log(`\n🔗 Associando jogos ao bolão...`)
    
    // Buscar jogos do Brasileirão
    const [jogosBrasileiro] = await connection.execute(`
      SELECT j.id, tc.nome as casa, tf.nome as fora, c.nome as campeonato
      FROM jogos j
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE c.nome = 'Campeonato Brasileiro Série A'
      LIMIT 5
    `)
    
    // Buscar jogos da Premier League
    const [jogosPremier] = await connection.execute(`
      SELECT j.id, tc.nome as casa, tf.nome as fora, c.nome as campeonato
      FROM jogos j
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE c.nome = 'Premier League'
      LIMIT 5
    `)
    
    // Buscar jogos da Ligue 1
    const [jogosLigue1] = await connection.execute(`
      SELECT j.id, tc.nome as casa, tf.nome as fora, c.nome as campeonato
      FROM jogos j
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE c.nome = 'Ligue 1'
      LIMIT 5
    `)
    
    // Buscar jogos do European Championship
    const [jogosEuro] = await connection.execute(`
      SELECT j.id, tc.nome as casa, tf.nome as fora, c.nome as campeonato
      FROM jogos j
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE c.nome = 'European Championship'
      LIMIT 5
    `)
    
    // Associar todos os jogos ao bolão
    const todosJogos = [...jogosBrasileiro, ...jogosPremier, ...jogosLigue1, ...jogosEuro]
    
    console.log(`📊 Jogos a serem associados: ${todosJogos.length}`)
    
    for (const jogo of todosJogos) {
      try {
        await connection.execute(`
          INSERT INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolaoId, jogo.id])
        
        console.log(`  ✅ ${jogo.casa} vs ${jogo.fora} (${jogo.campeonato})`)
      } catch (error) {
        if (!error.message.includes('Duplicate entry')) {
          console.log(`  ❌ Erro: ${error.message}`)
        }
      }
    }
    
    // Verificar o resultado
    console.log(`\n📊 Verificando bolão criado...`)
    
    const [bolaoVerificacao] = await connection.execute(`
      SELECT 
        b.nome,
        b.status,
        COUNT(bj.jogo_id) as total_jogos
      FROM boloes b
      LEFT JOIN bolao_jogos bj ON b.id = bj.bolao_id
      WHERE b.id = ?
      GROUP BY b.id
    `, [bolaoId])
    
    if (bolaoVerificacao.length > 0) {
      const bolao = bolaoVerificacao[0]
      console.log(`✅ Bolão: ${bolao.nome}`)
      console.log(`📊 Status: ${bolao.status}`)
      console.log(`⚽ Total de jogos: ${bolao.total_jogos}`)
    }
    
    // Verificar campeonatos no bolão
    const [campeonatosNoBolao] = await connection.execute(`
      SELECT 
        c.nome as campeonato,
        COUNT(j.id) as jogos
      FROM bolao_jogos bj
      JOIN jogos j ON bj.jogo_id = j.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE bj.bolao_id = ?
      GROUP BY c.id, c.nome
      ORDER BY jogos DESC
    `, [bolaoId])
    
    console.log(`\n🏆 Campeonatos no bolão:`)
    campeonatosNoBolao.forEach((camp, index) => {
      console.log(`  ${index + 1}. ${camp.campeonato}: ${camp.jogos} jogos`)
    })

    await connection.end()
    
    console.log(`\n🎉 Bolão multi-campeonatos criado com sucesso!`)
    console.log(`\n📋 Agora você pode testar:`)
    console.log(`   1. A API /api/boloes deve retornar TODOS os jogos dos campeonatos`)
    console.log(`   2. Na página principal, os jogos devem aparecer separados por campeonato`)
    console.log(`   3. Cada seção mostra todas as partidas do campeonato`)

  } catch (error) {
    console.error('❌ Erro ao criar bolão:', error.message)
  }
}

createBolaoMultiplosCampeonatos()
