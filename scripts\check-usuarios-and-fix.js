import mysql from "mysql2/promise"

async function checkUsuariosAndFix() {
  try {
    console.log('🔍 Verificando usuários e corrigindo problema dos bilhetes...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })

    // Verificar usuários existentes
    console.log('👥 Verificando usuários existentes...')
    const [usuarios] = await connection.execute(`
      SELECT id, nome, email, cpf FROM usuarios ORDER BY id DESC LIMIT 10
    `)

    console.log(`📊 Total de usuários encontrados: ${usuarios.length}`)
    if (usuarios.length > 0) {
      console.log('Últimos usuários:')
      usuarios.forEach((user, index) => {
        console.log(`  ${index + 1}. ID: ${user.id} - Nome: ${user.nome} - Email: ${user.email}`)
      })
    }

    // Se não há usuários, criar um usuário de teste
    let usuarioTeste = null
    if (usuarios.length === 0) {
      console.log('\n👤 Criando usuário de teste...')
      
      const [insertUser] = await connection.execute(`
        INSERT INTO usuarios (nome, email, cpf, senha, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
      `, ['moramed', '<EMAIL>', '762.368.020-09', 'senha123'])
      
      usuarioTeste = {
        id: insertUser.insertId,
        nome: 'moramed',
        email: '<EMAIL>',
        cpf: '762.368.020-09'
      }
      
      console.log(`✅ Usuário criado com ID: ${usuarioTeste.id}`)
    } else {
      usuarioTeste = usuarios[0]
    }

    // Agora inserir os bilhetes reais
    console.log('\n💾 Inserindo bilhetes reais...')
    
    const bilhetes = [
      {
        codigo: 'BLT17526411592332151YS3W',
        transaction_id: 'pixi_01k08rvf1wfk9bg8z3t0zq1at9',
        valor_total: 0.60,
        quantidade_apostas: 11
      },
      {
        codigo: 'BLT175264119146122Z6GZDN',
        transaction_id: 'pixi_01k08rwejvf46s72a6bjk246fp',
        valor_total: 0.60,
        quantidade_apostas: 11
      }
    ]

    const bilhetesInseridos = []

    for (const bilheteData of bilhetes) {
      try {
        const [insertResult] = await connection.execute(`
          INSERT INTO bilhetes (
            codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
            valor_total, quantidade_apostas, status, qr_code_pix, transaction_id,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          bilheteData.codigo,
          usuarioTeste.id,
          usuarioTeste.nome,
          usuarioTeste.email,
          usuarioTeste.cpf,
          bilheteData.valor_total,
          bilheteData.quantidade_apostas,
          'pendente',
          '00020126810014br.gov.bcb.pix2559qr-code.picpay.com...',
          bilheteData.transaction_id
        ])

        bilhetesInseridos.push({
          id: insertResult.insertId,
          codigo: bilheteData.codigo,
          transaction_id: bilheteData.transaction_id
        })

        console.log(`✅ Bilhete ${bilheteData.codigo} inserido com ID: ${insertResult.insertId}`)
        
      } catch (insertError) {
        console.error(`❌ Erro ao inserir bilhete ${bilheteData.codigo}:`, insertError.message)
      }
    }

    // Verificar se os bilhetes foram inseridos
    console.log('\n📊 Verificando bilhetes inseridos...')
    const [bilhetesVerificacao] = await connection.execute(`
      SELECT id, codigo, transaction_id, status, valor_total FROM bilhetes
    `)

    console.log(`Total de bilhetes no banco: ${bilhetesVerificacao.length}`)
    bilhetesVerificacao.forEach((bilhete, index) => {
      console.log(`  ${index + 1}. ${bilhete.codigo} - ${bilhete.transaction_id} - ${bilhete.status} - R$ ${bilhete.valor_total}`)
    })

    // Testar webhook com os bilhetes inseridos
    console.log('\n🔗 Testando webhook com bilhetes reais...')
    
    for (const bilhete of bilhetesInseridos) {
      console.log(`\n💰 Testando pagamento do bilhete ${bilhete.codigo}...`)
      
      const webhookData = {
        order_id: bilhete.transaction_id,
        transaction_id: bilhete.transaction_id,
        status: 'PAID',
        type: 'payment',
        message: 'Pagamento aprovado',
        amount: 0.60,
        payment_method: 'pix'
      }

      try {
        const webhookResponse = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(webhookData)
        })

        const webhookResult = await webhookResponse.json()
        
        console.log(`   Status: ${webhookResponse.status}`)
        console.log(`   Resposta: ${webhookResult.message || webhookResult.error || 'N/A'}`)
        
        // Verificar se o status foi atualizado
        const [statusAtualizado] = await connection.execute(`
          SELECT status FROM bilhetes WHERE id = ?
        `, [bilhete.id])
        
        console.log(`   Status no banco: ${statusAtualizado[0].status}`)
        
      } catch (webhookError) {
        console.error(`   ❌ Erro no webhook: ${webhookError.message}`)
      }
    }

    await connection.end()
    
    console.log('\n✅ Teste concluído!')

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
  }
}

checkUsuariosAndFix()
