import mysql from "mysql2/promise"

async function findIds106107() {
  try {
    console.log('🔍 Procurando registros com ID 106 e 107 em TODAS as tabelas...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      charset: 'utf8mb4'
    })

    // Bancos para verificar
    const bancos = [
      'api-games-2025',
      'bigbasssplash', 
      'china_alex',
      'gerador_pro',
      'pragmatic',
      'sistema-bolao-top',
      'taxi-uber-taxi',
      'taxi_uber_taxi'
    ]

    for (const banco of bancos) {
      console.log(`\n📊 Verificando banco: ${banco}`)
      
      try {
        // Listar todas as tabelas do banco
        const [tables] = await connection.execute(`
          SELECT TABLE_NAME 
          FROM information_schema.tables 
          WHERE table_schema = ?
        `, [banco])

        for (const table of tables) {
          const tableName = table.TABLE_NAME
          
          try {
            // Verificar se a tabela tem coluna 'id'
            const [columns] = await connection.execute(`
              SELECT COLUMN_NAME 
              FROM information_schema.columns 
              WHERE table_schema = ? AND table_name = ? AND column_name = 'id'
            `, [banco, tableName])
            
            if (columns.length > 0) {
              // Procurar pelos IDs 106 e 107
              const [records] = await connection.execute(`
                SELECT * 
                FROM \`${banco}\`.\`${tableName}\`
                WHERE id IN (106, 107)
              `)
              
              if (records.length > 0) {
                console.log(`   🎯 ENCONTRADO na tabela ${tableName}:`)
                records.forEach((record, index) => {
                  console.log(`     ${index + 1}. ID: ${record.id}`)
                  console.log(`        Dados: ${JSON.stringify(record)}`)
                })
              }
            }
            
          } catch (error) {
            // Ignorar erros de tabelas que não podemos acessar
          }
        }
        
      } catch (error) {
        console.log(`   ❌ Erro ao verificar banco ${banco}: ${error.message}`)
      }
    }

    // Também procurar por códigos específicos em qualquer coluna
    console.log('\n🔍 Procurando por códigos específicos dos bilhetes...')
    
    const codigosParaProcurar = [
      'BLT17526411592332151YS3W',
      'BLT175264119146122Z6GZDN',
      'pixi_01k08rvf1wfk9bg8z3t0zq1at9',
      'pixi_01k08rwejvf46s72a6bjk246fp'
    ]

    for (const banco of bancos) {
      console.log(`\n📊 Procurando códigos no banco: ${banco}`)
      
      try {
        // Listar todas as tabelas do banco
        const [tables] = await connection.execute(`
          SELECT TABLE_NAME 
          FROM information_schema.tables 
          WHERE table_schema = ?
        `, [banco])

        for (const table of tables) {
          const tableName = table.TABLE_NAME
          
          try {
            // Verificar se a tabela tem registros
            const [count] = await connection.execute(`
              SELECT COUNT(*) as total 
              FROM \`${banco}\`.\`${tableName}\`
            `)
            
            if (count[0].total > 0) {
              // Procurar pelos códigos em qualquer coluna de texto
              for (const codigo of codigosParaProcurar) {
                try {
                  const [records] = await connection.execute(`
                    SELECT * 
                    FROM \`${banco}\`.\`${tableName}\`
                    WHERE CONCAT_WS('|', *) LIKE ?
                    LIMIT 1
                  `, [`%${codigo}%`])
                  
                  if (records.length > 0) {
                    console.log(`   🎯 CÓDIGO ENCONTRADO: ${codigo}`)
                    console.log(`      Tabela: ${tableName}`)
                    console.log(`      Dados: ${JSON.stringify(records[0])}`)
                  }
                } catch (error) {
                  // Ignorar erros de sintaxe SQL
                }
              }
            }
            
          } catch (error) {
            // Ignorar erros de tabelas que não podemos acessar
          }
        }
        
      } catch (error) {
        console.log(`   ❌ Erro ao verificar banco ${banco}: ${error.message}`)
      }
    }

    await connection.end()
    
    console.log('\n✅ Busca concluída!')

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
  }
}

findIds106107()
