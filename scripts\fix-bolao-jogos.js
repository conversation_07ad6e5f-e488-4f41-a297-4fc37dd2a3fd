import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function fixBolaoJogos() {
  try {
    console.log('🔧 Corrigindo jogos do bolão...')
    
    await initializeDatabase()

    // Buscar o bolão criado
    const boloes = await executeQuery(`
      SELECT id, nome, partidas_selecionadas 
      FROM boloes 
      WHERE id = 75
    `)

    if (boloes.length === 0) {
      console.log('❌ Bolão ID 75 não encontrado')
      return
    }

    const bolao = boloes[0]
    console.log(`📋 Processando bolão: ${bolao.nome}`)

    // Parse das partidas selecionadas
    let partidasSelecionadas = []
    try {
      partidasSelecionadas = JSON.parse(bolao.partidas_selecionadas || '[]')
    } catch (error) {
      console.log('❌ Erro ao fazer parse das partidas selecionadas')
      return
    }

    console.log(`🎮 Encontradas ${partidasSelecionadas.length} partidas para processar`)

    if (partidasSelecionadas.length === 0) {
      console.log('❌ Nenhuma partida encontrada no bolão')
      return
    }

    // Processar cada partida
    for (const partida of partidasSelecionadas) {
      try {
        console.log(`⚽ Processando partida: ${partida.time_casa_nome} vs ${partida.time_fora_nome}`)

        // Verificar se o jogo já existe
        let jogoExistente = await executeQuery(`
          SELECT id FROM jogos WHERE id = ?
        `, [partida.id])

        let jogoId = partida.id

        // Se o jogo não existe, criar ele
        if (!jogoExistente || jogoExistente.length === 0) {
          console.log(`  🆕 Criando jogo ${partida.id}...`)
          
          // Buscar ou criar time da casa
          await executeQuery(`
            INSERT IGNORE INTO times (nome, logo_url) VALUES (?, ?)
          `, [partida.time_casa_nome, partida.time_casa_logo])

          // Buscar ou criar time visitante
          await executeQuery(`
            INSERT IGNORE INTO times (nome, logo_url) VALUES (?, ?)
          `, [partida.time_fora_nome, partida.time_fora_logo])

          // Buscar IDs dos times
          const timeCasa = await executeQuery(`
            SELECT id FROM times WHERE nome = ? LIMIT 1
          `, [partida.time_casa_nome])

          const timeFora = await executeQuery(`
            SELECT id FROM times WHERE nome = ? LIMIT 1
          `, [partida.time_fora_nome])

          // Buscar campeonato
          const campeonato = await executeQuery(`
            SELECT id FROM campeonatos WHERE codigo = ? OR nome LIKE ? LIMIT 1
          `, [partida.campeonato_id, `%${partida.campeonato_nome}%`])

          if (timeCasa.length > 0 && timeFora.length > 0 && campeonato.length > 0) {
            await executeQuery(`
              INSERT INTO jogos (
                id, time_casa_id, time_fora_id, campeonato_id, 
                data_jogo, status, rodada
              ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
              partida.id,
              timeCasa[0].id,
              timeFora[0].id,
              campeonato[0].id,
              partida.data_jogo,
              partida.status || 'agendado',
              partida.matchday || 1
            ])
            
            console.log(`  ✅ Jogo ${partida.id} criado`)
          } else {
            console.log(`  ❌ Não foi possível criar jogo ${partida.id} - times ou campeonato não encontrados`)
            continue
          }
        } else {
          console.log(`  ✅ Jogo ${partida.id} já existe`)
        }

        // Inserir na tabela bolao_jogos
        await executeQuery(`
          INSERT IGNORE INTO bolao_jogos (bolao_id, jogo_id) VALUES (?, ?)
        `, [bolao.id, jogoId])

        console.log(`  ✅ Jogo ${jogoId} associado ao bolão`)

      } catch (jogoError) {
        console.error(`  ❌ Erro ao processar jogo ${partida.id}:`, jogoError.message)
      }
    }

    // Verificar resultado final
    const jogosAssociados = await executeQuery(`
      SELECT COUNT(*) as total
      FROM bolao_jogos 
      WHERE bolao_id = ?
    `, [bolao.id])

    console.log('')
    console.log(`🎉 Processamento concluído!`)
    console.log(`📊 Total de jogos associados ao bolão: ${jogosAssociados[0].total}`)

  } catch (error) {
    console.error('❌ Erro ao corrigir jogos do bolão:', error)
  }
}

fixBolaoJogos()
