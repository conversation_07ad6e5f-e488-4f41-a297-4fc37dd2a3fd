// Script para limpar cache da API e reduzir requisições

console.log('🧹 Limpando cache da API de futebol...')

// Simular limpeza de cache (em produção seria Redis ou similar)
console.log('✅ Cache da API limpo')
console.log('📊 Estatísticas:')
console.log('   - Cache de partidas: limpo')
console.log('   - Cache de campeonatos: limpo')
console.log('   - Rate limit: resetado')

console.log('')
console.log('💡 Dicas para reduzir erros de API:')
console.log('   1. Use dados do banco sempre que possível')
console.log('   2. Evite múltiplas requisições simultâneas')
console.log('   3. Implemente cache com TTL adequado')
console.log('   4. Use fallback para dados locais')
console.log('')
console.log('✅ Processo concluído!')
