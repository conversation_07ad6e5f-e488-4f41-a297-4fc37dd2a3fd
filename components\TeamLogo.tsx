import React, { useState } from 'react'
import Image from 'next/image'

interface TeamLogoProps {
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const TeamLogo: React.FC<TeamLogoProps> = ({ 
  src, 
  alt, 
  size = 'md', 
  className = '' 
}) => {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  const fallbackImage = '/images/team-placeholder.png'

  // Tentar diferentes formatos de URL se a original falhar
  let imageSrc = src
  if (imageError || !src) {
    imageSrc = fallbackImage
  } else if (src && !src.startsWith('http')) {
    // Se não é uma URL completa, tentar construir uma
    imageSrc = `https://crests.football-data.org/${src}.png`
  } else if (src && src.includes('/images/teams/')) {
    // Para URLs locais de times, verificar se existe
    imageSrc = src
  }

  const handleImageError = () => {
    setImageError(true)
    setIsLoading(false)
  }

  const handleImageLoad = () => {
    setIsLoading(false)
  }

  return (
    <div className={`${sizeClasses[size]} relative flex items-center justify-center ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-full" />
      )}
      
      <Image
        src={imageSrc}
        alt={alt}
        width={size === 'sm' ? 24 : size === 'md' ? 32 : 48}
        height={size === 'sm' ? 24 : size === 'md' ? 32 : 48}
        className={`rounded-full object-contain ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        unoptimized // Para permitir URLs externas
      />
      
      {imageError && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-md border border-white/20">
          <span className={`font-bold text-white ${size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'}`}>
            {(() => {
              // Melhor extração de iniciais
              const words = alt.trim().split(' ').filter(word => word.length > 0)
              if (words.length >= 2) {
                return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase()
              } else if (words.length === 1) {
                return words[0].substring(0, 2).toUpperCase()
              }
              return 'TC'
            })()}
          </span>
        </div>
      )}
    </div>
  )
}

export default TeamLogo
