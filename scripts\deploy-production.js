#!/usr/bin/env node

import { spawn, exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs'

const execAsync = promisify(exec)

async function deployProduction() {
  try {
    console.log('🚀 Iniciando deploy para produção...')
    console.log('🌐 Domínio: https://rrsistemasbolaodemo.site')
    
    // 1. Verificar se estamos no diretório correto
    if (!fs.existsSync('package.json')) {
      throw new Error('Execute este script na raiz do projeto')
    }
    
    // 2. Parar processos existentes
    console.log('\n🛑 Parando processos existentes...')
    try {
      if (process.platform === 'win32') {
        await execAsync('taskkill /F /IM node.exe')
      } else {
        await execAsync('pkill -f "next"')
        await execAsync('pkill -f "node"')
      }
    } catch (error) {
      console.log('⚠️ Nenhum processo para parar')
    }
    
    // 3. Limpar cache
    console.log('\n🧹 Limpando cache...')
    try {
      await execAsync('rm -rf .next')
    } catch (error) {
      console.log('⚠️ Cache já limpo')
    }
    
    // 4. Instalar dependências
    console.log('\n📦 Instalando dependências...')
    await execAsync('npm install')
    
    // 5. Verificar banco de dados
    console.log('\n🗄️ Verificando banco de dados...')
    try {
      await execAsync('npm run db:check')
      console.log('✅ Banco de dados OK')
    } catch (error) {
      console.log('⚠️ Problema no banco, tentando corrigir...')
      await execAsync('npm run db:setup')
      await execAsync('npm run db:fix-boloes')
    }
    
    // 6. Build para produção
    console.log('\n🔨 Fazendo build para produção...')
    await execAsync('npm run build:prod')
    
    // 7. Iniciar servidor de produção
    console.log('\n🚀 Iniciando servidor de produção...')
    console.log('📍 URL: https://rrsistemasbolaodemo.site')
    console.log('🔧 Porta: 80 (padrão HTTP)')
    
    // Criar processo em background
    const server = spawn('npm', ['run', 'start:prod'], {
      stdio: 'inherit',
      shell: true,
      detached: true,
      env: {
        ...process.env,
        NODE_ENV: 'production',
        PORT: '80'
      }
    })
    
    server.on('error', (error) => {
      console.error('❌ Erro ao iniciar servidor:', error)
      process.exit(1)
    })
    
    // Aguardar um pouco para verificar se iniciou
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    console.log('\n✅ Deploy concluído!')
    console.log('🌐 Site disponível em: https://rrsistemasbolaodemo.site')
    console.log('🔧 Admin: https://rrsistemasbolaodemo.site/admin')
    console.log('📊 Dashboard: https://rrsistemasbolaodemo.site/dashboard')
    
    console.log('\n📋 Para monitorar:')
    console.log('  - Logs: pm2 logs (se usando PM2)')
    console.log('  - Status: ps aux | grep node')
    console.log('  - Parar: pkill -f "next"')
    
  } catch (error) {
    console.error('❌ Erro no deploy:', error)
    process.exit(1)
  }
}

// Verificar se é para rodar na porta 3000 (desenvolvimento em servidor)
const args = process.argv.slice(2)
if (args.includes('--port-3000')) {
  console.log('🔧 Modo desenvolvimento em servidor (porta 3000)')
  // Modificar para usar porta 3000
  process.env.PORT = '3000'
}

deployProduction()
