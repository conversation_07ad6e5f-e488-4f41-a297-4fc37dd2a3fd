import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function optimizeSystem() {
  try {
    console.log('🚀 Iniciando otimização do sistema...')
    
    await initializeDatabase()

    // 1. Verificar e otimizar índices do banco
    console.log('📊 Verificando índices do banco de dados...')
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_jogos_data ON jogos(data_jogo)',
      'CREATE INDEX IF NOT EXISTS idx_jogos_status ON jogos(status)',
      'CREATE INDEX IF NOT EXISTS idx_jogos_campeonato ON jogos(campeonato_id)',
      'CREATE INDEX IF NOT EXISTS idx_apostas_usuario ON apostas(usuario_id)',
      'CREATE INDEX IF NOT EXISTS idx_apostas_bolao ON apostas(bolao_id)',
      'CREATE INDEX IF NOT EXISTS idx_apostas_status ON apostas(status)',
      'CREATE INDEX IF NOT EXISTS idx_pagamentos_usuario ON pagamentos(usuario_id)',
      'CREATE INDEX IF NOT EXISTS idx_pagamentos_status ON pagamentos(status)',
      'CREATE INDEX IF NOT EXISTS idx_times_api ON times(api_id)',
      'CREATE INDEX IF NOT EXISTS idx_campeonatos_status ON campeonatos(status)'
    ]

    for (const indexQuery of indexes) {
      try {
        await executeQuery(indexQuery)
        console.log(`✅ Índice criado: ${indexQuery.split(' ')[5]}`)
      } catch (error) {
        console.log(`⚠️ Índice já existe: ${indexQuery.split(' ')[5]}`)
      }
    }

    // 2. Limpar dados antigos desnecessários
    console.log('🧹 Limpando dados antigos...')
    
    // Limpar logs de webhook antigos (mais de 30 dias)
    try {
      const deletedLogs = await executeQuery(`
        DELETE FROM webhook_logs
        WHERE data_criacao < DATE_SUB(NOW(), INTERVAL 30 DAY)
      `)
      console.log(`✅ ${deletedLogs.affectedRows || 0} logs antigos removidos`)
    } catch (error) {
      console.log(`⚠️ Tabela webhook_logs não encontrada ou sem dados antigos`)
    }

    // 3. Atualizar estatísticas das tabelas
    console.log('📈 Atualizando estatísticas...')
    
    const stats = await executeQuery(`
      SELECT 
        (SELECT COUNT(*) FROM usuarios) as total_usuarios,
        (SELECT COUNT(*) FROM boloes WHERE status = 'ativo') as boloes_ativos,
        (SELECT COUNT(*) FROM apostas WHERE status = 'paga') as apostas_pagas,
        (SELECT COUNT(*) FROM jogos WHERE data_jogo > NOW()) as jogos_futuros,
        (SELECT COUNT(*) FROM times) as total_times,
        (SELECT COUNT(*) FROM campeonatos WHERE status = 'ativo') as campeonatos_ativos
    `)

    console.log('📊 Estatísticas do sistema:')
    console.log(`   - Usuários: ${stats[0].total_usuarios}`)
    console.log(`   - Bolões ativos: ${stats[0].boloes_ativos}`)
    console.log(`   - Apostas pagas: ${stats[0].apostas_pagas}`)
    console.log(`   - Jogos futuros: ${stats[0].jogos_futuros}`)
    console.log(`   - Times: ${stats[0].total_times}`)
    console.log(`   - Campeonatos ativos: ${stats[0].campeonatos_ativos}`)

    // 4. Verificar integridade dos dados
    console.log('🔍 Verificando integridade dos dados...')
    
    const orphanedData = await executeQuery(`
      SELECT 
        (SELECT COUNT(*) FROM apostas a WHERE NOT EXISTS (SELECT 1 FROM usuarios u WHERE u.id = a.usuario_id)) as apostas_sem_usuario,
        (SELECT COUNT(*) FROM apostas a WHERE NOT EXISTS (SELECT 1 FROM boloes b WHERE b.id = a.bolao_id)) as apostas_sem_bolao,
        (SELECT COUNT(*) FROM jogos j WHERE NOT EXISTS (SELECT 1 FROM times tc WHERE tc.id = j.time_casa_id)) as jogos_sem_time_casa,
        (SELECT COUNT(*) FROM jogos j WHERE NOT EXISTS (SELECT 1 FROM times tf WHERE tf.id = j.time_fora_id)) as jogos_sem_time_fora
    `)

    const issues = orphanedData[0]
    let hasIssues = false

    if (issues.apostas_sem_usuario > 0) {
      console.log(`⚠️ ${issues.apostas_sem_usuario} apostas sem usuário válido`)
      hasIssues = true
    }
    if (issues.apostas_sem_bolao > 0) {
      console.log(`⚠️ ${issues.apostas_sem_bolao} apostas sem bolão válido`)
      hasIssues = true
    }
    if (issues.jogos_sem_time_casa > 0) {
      console.log(`⚠️ ${issues.jogos_sem_time_casa} jogos sem time da casa válido`)
      hasIssues = true
    }
    if (issues.jogos_sem_time_fora > 0) {
      console.log(`⚠️ ${issues.jogos_sem_time_fora} jogos sem time visitante válido`)
      hasIssues = true
    }

    if (!hasIssues) {
      console.log('✅ Integridade dos dados verificada - tudo OK!')
    }

    // 5. Otimizar configurações
    console.log('⚙️ Aplicando otimizações...')
    
    // Configurar variáveis do MySQL para melhor performance
    const optimizations = [
      'SET SESSION query_cache_type = ON',
      'SET SESSION query_cache_size = 1048576'
    ]

    for (const opt of optimizations) {
      try {
        await executeQuery(opt)
        console.log(`✅ Otimização aplicada: ${opt.split(' ')[2]}`)
      } catch (error) {
        console.log(`⚠️ Otimização não aplicada: ${opt.split(' ')[2]}`)
      }
    }

    console.log('')
    console.log('🎉 Otimização do sistema concluída!')
    console.log('')
    console.log('💡 Recomendações para melhor performance:')
    console.log('   1. Use cache Redis em produção')
    console.log('   2. Implemente CDN para assets estáticos')
    console.log('   3. Configure backup automático do banco')
    console.log('   4. Monitore logs de erro regularmente')
    console.log('   5. Use rate limiting para APIs externas')

  } catch (error) {
    console.error('❌ Erro durante otimização:', error)
  }
}

optimizeSystem()
