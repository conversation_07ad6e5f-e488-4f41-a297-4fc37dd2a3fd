// Testar criação completa de bolão com campeonatos

async function testFullBolao() {
  try {
    console.log('🚀 Testando criação completa de bolão...')
    
    const dadosBolao = {
      nome: 'Bolão Multi-Campeonatos 2025',
      descricao: 'Bolão com múltiplos campeonatos sincronizados',
      valor_aposta: 30.00,
      premio_total: 8000.00,
      data_inicio: new Date().toISOString(),
      data_fim: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
      max_participantes: 300,
      banner_image: '/uploads/banners/banner_multi.webp',
      campeonatos_selecionados: [
        { id: 2013, nome: 'Campeonato Brasileiro Série A', codigo: 'BSA' },
        { id: 2021, nome: 'Premier League', codigo: 'PL' },
        { id: 2001, nome: 'UEFA Champions League', codigo: 'CL' }
      ],
      partidas_selecionadas: []
    }
    
    console.log('📦 Dados do bolão:', {
      nome: dadosBolao.nome,
      valor_aposta: dadosBolao.valor_aposta,
      premio_total: dadosBolao.premio_total,
      campeonatos: dadosBolao.campeonatos_selecionados.length
    })
    
    // 1. Criar bolão
    console.log('\n1️⃣ Criando bolão...')
    const response = await fetch('http://localhost:3000/api/admin/boloes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dadosBolao)
    })
    
    const result = await response.json()
    
    console.log(`📡 Status: ${response.status}`)
    console.log(`📋 Resultado:`, result)
    
    if (!response.ok) {
      console.log('❌ Erro na criação')
      return
    }
    
    const bolaoId = result.bolao.id
    console.log(`✅ Bolão criado com ID: ${bolaoId}`)
    
    // 2. Verificar na listagem pública
    console.log('\n2️⃣ Verificando na listagem pública...')
    const listResponse = await fetch('http://localhost:3000/api/boloes')
    const listResult = await listResponse.json()
    
    console.log(`📊 Bolões na listagem: ${listResult.boloes.length}`)
    
    if (listResult.boloes.length > 0) {
      listResult.boloes.forEach((bolao, index) => {
        console.log(`  ${index + 1}. ${bolao.nome} (ID: ${bolao.id})`)
        console.log(`     Status: ${bolao.status}`)
        console.log(`     Valor: R$ ${bolao.valor_aposta}`)
        console.log(`     Prêmio: R$ ${bolao.premio_total}`)
        console.log(`     Campeonatos: ${bolao.campeonatos_selecionados.length}`)
        
        if (bolao.campeonatos_selecionados.length > 0) {
          bolao.campeonatos_selecionados.forEach((camp, i) => {
            console.log(`       ${i + 1}. ${camp.nome} (${camp.codigo})`)
          })
        }
        console.log('')
      })
    }
    
    // 3. Verificar na listagem admin
    console.log('3️⃣ Verificando na listagem admin...')
    const adminResponse = await fetch('http://localhost:3000/api/admin/boloes')
    const adminResult = await adminResponse.json()
    
    console.log(`📊 Bolões no admin: ${adminResult.boloes.length}`)
    console.log(`📈 Stats: ${JSON.stringify(adminResult.stats)}`)
    
    console.log('\n✅ Teste completo finalizado!')

  } catch (error) {
    console.error('❌ Erro no teste:', error.message)
  }
}

testFullBolao()
