import mysql from "mysql2/promise"

async function searchBilhetesEverywhere() {
  try {
    console.log('🔍 Procurando bilhetes em TODOS os bancos...')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      charset: 'utf8mb4'
    })

    // Bancos para verificar
    const bancos = [
      'api-games-2025',
      'bigbasssplash', 
      'china_alex',
      'gerador_pro',
      'pragmatic',
      'sistema-bolao-top',
      'taxi-uber-taxi',
      'taxi_uber_taxi'
    ]

    for (const banco of bancos) {
      console.log(`\n📊 Verificando banco: ${banco}`)
      
      try {
        // Procurar tabelas que podem conter bilhetes
        const [tables] = await connection.execute(`
          SELECT TABLE_NAME 
          FROM information_schema.tables 
          WHERE table_schema = ? 
          AND (TABLE_NAME LIKE '%bilhet%' 
               OR TABLE_NAME LIKE '%ticket%' 
               OR TABLE_NAME LIKE '%payment%'
               OR TABLE_NAME LIKE '%order%'
               OR TABLE_NAME LIKE '%transac%')
        `, [banco])

        if (tables.length > 0) {
          console.log(`   📋 Tabelas relacionadas encontradas:`)
          
          for (const table of tables) {
            const tableName = table.TABLE_NAME
            console.log(`     - ${tableName}`)
            
            try {
              // Verificar se tem registros
              const [count] = await connection.execute(`
                SELECT COUNT(*) as total 
                FROM \`${banco}\`.\`${tableName}\`
              `)
              
              console.log(`       📊 Registros: ${count[0].total}`)
              
              if (count[0].total > 0) {
                // Procurar pelos códigos específicos dos bilhetes
                const [specific] = await connection.execute(`
                  SELECT * 
                  FROM \`${banco}\`.\`${tableName}\`
                  WHERE (codigo LIKE '%BLT175264115923%' 
                         OR codigo LIKE '%BLT175264119146%'
                         OR transaction_id LIKE '%pixi_01k08rv%'
                         OR id IN (106, 107))
                  LIMIT 5
                `)
                
                if (specific.length > 0) {
                  console.log(`       🎯 BILHETES ESPECÍFICOS ENCONTRADOS!`)
                  specific.forEach((record, index) => {
                    console.log(`         ${index + 1}. ${JSON.stringify(record)}`)
                  })
                } else {
                  // Mostrar alguns registros para análise
                  const [sample] = await connection.execute(`
                    SELECT * 
                    FROM \`${banco}\`.\`${tableName}\`
                    ORDER BY id DESC 
                    LIMIT 3
                  `)
                  
                  if (sample.length > 0) {
                    console.log(`       📄 Últimos registros:`)
                    sample.forEach((record, index) => {
                      console.log(`         ${index + 1}. ${JSON.stringify(record)}`)
                    })
                  }
                }
              }
              
            } catch (error) {
              console.log(`       ❌ Erro ao verificar ${tableName}: ${error.message}`)
            }
          }
        } else {
          console.log(`   ❌ Nenhuma tabela relacionada encontrada`)
        }
        
      } catch (error) {
        console.log(`   ❌ Erro ao verificar banco ${banco}: ${error.message}`)
      }
    }

    await connection.end()
    
    console.log('\n🔍 Busca concluída!')

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
  }
}

searchBilhetesEverywhere()
