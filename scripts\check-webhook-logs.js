import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function checkWebhookLogs() {
  try {
    console.log('🔍 Verificando tabela webhook_logs...')
    
    await initializeDatabase()

    // Verificar se a tabela existe
    try {
      const tableExists = await executeQuery(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = 'sistema-bolao-top' 
        AND table_name = 'webhook_logs'
      `)

      if (tableExists[0].count === 0) {
        console.log('❌ Tabela webhook_logs não existe. Criando...')
        
        await executeQuery(`
          CREATE TABLE webhook_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            transaction_id VARCHAR(255),
            order_id VARCHAR(255),
            status VARCHAR(50),
            webhook_data JSON,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_transaction_id (transaction_id),
            INDEX idx_order_id (order_id),
            INDEX idx_status (status),
            INDEX idx_data_criacao (data_criacao)
          )
        `)
        
        console.log('✅ Tabela webhook_logs criada com sucesso')
      } else {
        console.log('✅ Tabela webhook_logs já existe')
      }

      // Verificar estrutura da tabela
      const columns = await executeQuery(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM information_schema.columns 
        WHERE table_schema = 'sistema-bolao-top' 
        AND table_name = 'webhook_logs'
        ORDER BY ORDINAL_POSITION
      `)

      console.log('\n📋 Estrutura da tabela webhook_logs:')
      columns.forEach(col => {
        console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? '(NULL)' : '(NOT NULL)'}`)
      })

      // Verificar dados existentes
      const count = await executeQuery(`
        SELECT COUNT(*) as total FROM webhook_logs
      `)

      console.log(`\n📊 Total de registros: ${count[0].total}`)

      if (count[0].total > 0) {
        const recent = await executeQuery(`
          SELECT id, transaction_id, order_id, status, data_criacao
          FROM webhook_logs 
          ORDER BY id DESC 
          LIMIT 5
        `)

        console.log('\n📝 Últimos 5 registros:')
        recent.forEach((log, index) => {
          console.log(`   ${index + 1}. ID: ${log.id} | TxID: ${log.transaction_id} | Status: ${log.status} | Data: ${log.data_criacao}`)
        })
      }

    } catch (error) {
      console.error('❌ Erro ao verificar tabela:', error.message)
    }

  } catch (error) {
    console.error('❌ Erro geral:', error)
  }
}

checkWebhookLogs()
