import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🚀 INICIANDO SINCRONIZAÇÃO COMPLETA...")

    const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'YOUR_TOKEN_HERE'
    const FOOTBALL_API_BASE = 'https://api.football-data.org/v4'

    if (!FOOTBALL_API_TOKEN || FOOTBALL_API_TOKEN === 'YOUR_TOKEN_HERE') {
      // Usar dados mock expandidos se não tiver token
      return await syncMockDataExpanded()
    }

    const headers = {
      'X-Auth-Token': FOOTBALL_API_TOKEN,
      'Content-Type': 'application/json'
    }

    let totalStats = {
      competitions: { added: 0, updated: 0, total: 0 },
      teams: { added: 0, updated: 0, total: 0 },
      matches: { added: 0, total: 0 }
    }

    // 1. SINCRONIZAR TODAS AS COMPETIÇÕES
    console.log("🏆 FASE 1: Sincronizando competições...")
    
    try {
      const response = await fetch(`${FOOTBALL_API_BASE}/competitions`, { headers })
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} - ${response.statusText}`)
      }

      const data = await response.json()
      const competitions = data.competitions || []
      totalStats.competitions.total = competitions.length

      console.log(`📊 ${competitions.length} competições encontradas na API`)

      // Filtrar competições relevantes (mais abrangente)
      const relevantCompetitions = competitions.filter(comp => {
        const name = comp.name.toLowerCase()
        const area = comp.area?.name?.toLowerCase() || ''
        
        return (
          // Principais regiões
          area.includes('brazil') || area.includes('south america') ||
          area.includes('england') || area.includes('spain') || area.includes('italy') ||
          area.includes('germany') || area.includes('france') || area.includes('portugal') ||
          area.includes('netherlands') || area.includes('argentina') || area.includes('uruguay') ||
          area.includes('europe') || area.includes('world') ||
          
          // Competições específicas
          name.includes('premier league') || name.includes('la liga') || name.includes('serie a') ||
          name.includes('bundesliga') || name.includes('ligue 1') || name.includes('brasileirão') ||
          name.includes('brasileiro') || name.includes('copa') || name.includes('libertadores') ||
          name.includes('champions league') || name.includes('europa league') ||
          
          // Códigos importantes
          ['PL', 'PD', 'SA', 'BL1', 'FL1', 'PPL', 'DED', 'BSA', 'CLI', 'CSA', 'CL', 'EL'].includes(comp.code)
        )
      })

      console.log(`🎯 ${relevantCompetitions.length} competições relevantes selecionadas`)

      for (const comp of relevantCompetitions) {
        try {
          const existing = await executeQuery(`
            SELECT id FROM campeonatos WHERE api_id = ?
          `, [comp.id.toString()])

          const competitionData = {
            nome: comp.name,
            descricao: comp.name,
            pais: comp.area?.name || 'Internacional',
            temporada: comp.currentSeason?.startDate?.substring(0, 4) || '2024',
            status: 'ativo',
            data_inicio: comp.currentSeason?.startDate || null,
            data_fim: comp.currentSeason?.endDate || null,
            api_id: comp.id.toString(),
            logo_url: comp.emblem || null
          }

          if (existing.length === 0) {
            await executeQuery(`
              INSERT INTO campeonatos (
                nome, descricao, pais, temporada, status, 
                data_inicio, data_fim, api_id, logo_url, data_criacao
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `, [
              competitionData.nome, competitionData.descricao, competitionData.pais,
              competitionData.temporada, competitionData.status, competitionData.data_inicio,
              competitionData.data_fim, competitionData.api_id, competitionData.logo_url
            ])
            
            totalStats.competitions.added++
            console.log(`✅ Adicionada: ${comp.name}`)
          } else {
            await executeQuery(`
              UPDATE campeonatos 
              SET nome = ?, descricao = ?, pais = ?, temporada = ?, 
                  data_inicio = ?, data_fim = ?, logo_url = ?
              WHERE api_id = ?
            `, [
              competitionData.nome, competitionData.descricao, competitionData.pais,
              competitionData.temporada, competitionData.data_inicio, competitionData.data_fim,
              competitionData.logo_url, competitionData.api_id
            ])
            
            totalStats.competitions.updated++
            console.log(`🔄 Atualizada: ${comp.name}`)
          }

          await new Promise(resolve => setTimeout(resolve, 50)) // Rate limit

        } catch (compError) {
          console.error(`❌ Erro ao processar ${comp.name}:`, compError)
        }
      }

    } catch (apiError) {
      console.error('❌ Erro na API de competições:', apiError)
    }

    // 2. SINCRONIZAR TIMES DE CADA COMPETIÇÃO
    console.log("👥 FASE 2: Sincronizando times...")

    const competitions = await executeQuery(`
      SELECT id, api_id, nome FROM campeonatos 
      WHERE api_id IS NOT NULL AND status = 'ativo'
      ORDER BY id DESC
      LIMIT 20
    `)

    for (const competition of competitions) {
      try {
        console.log(`🔄 Buscando times de ${competition.nome}...`)
        
        const response = await fetch(
          `${FOOTBALL_API_BASE}/competitions/${competition.api_id}/teams`,
          { headers }
        )

        if (!response.ok) {
          console.log(`⚠️ Erro ao buscar times de ${competition.nome}: ${response.status}`)
          continue
        }

        const data = await response.json()
        const teams = data.teams || []

        console.log(`📊 ${teams.length} times encontrados em ${competition.nome}`)

        for (const team of teams) {
          try {
            const existingTeam = await executeQuery(`
              SELECT id FROM times WHERE api_id = ?
            `, [team.id.toString()])

            if (existingTeam.length === 0) {
              await executeQuery(`
                INSERT INTO times (
                  nome, nome_curto, cidade, pais, logo_url, api_id, data_criacao
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())
              `, [
                team.name,
                team.shortName || team.name.substring(0, 15),
                team.venue || null,
                team.area?.name || competition.pais || 'Internacional',
                team.crest || null,
                team.id.toString()
              ])
              
              totalStats.teams.added++
              console.log(`✅ Time adicionado: ${team.name}`)
            } else {
              await executeQuery(`
                UPDATE times 
                SET nome = ?, nome_curto = ?, cidade = ?, pais = ?, logo_url = ?
                WHERE api_id = ?
              `, [
                team.name,
                team.shortName || team.name.substring(0, 15),
                team.venue || existingTeam[0].cidade,
                team.area?.name || competition.pais || 'Internacional',
                team.crest || existingTeam[0].logo_url,
                team.id.toString()
              ])
              
              totalStats.teams.updated++
              console.log(`🔄 Time atualizado: ${team.name}`)
            }

          } catch (teamError) {
            console.error(`❌ Erro ao processar time ${team.name}:`, teamError)
          }
        }

        await new Promise(resolve => setTimeout(resolve, 300)) // Rate limit

      } catch (compError) {
        console.error(`❌ Erro ao processar times de ${competition.nome}:`, compError)
      }
    }

    // 3. SINCRONIZAR PARTIDAS
    console.log("⚽ FASE 3: Sincronizando partidas...")

    for (const competition of competitions.slice(0, 10)) { // Limitar para evitar timeout
      try {
        console.log(`🔄 Buscando partidas de ${competition.nome}...`)
        
        const response = await fetch(
          `${FOOTBALL_API_BASE}/competitions/${competition.api_id}/matches?status=SCHEDULED`,
          { headers }
        )

        if (!response.ok) {
          console.log(`⚠️ Erro ao buscar partidas de ${competition.nome}: ${response.status}`)
          continue
        }

        const data = await response.json()
        const matches = data.matches || []

        console.log(`📊 ${matches.length} partidas encontradas em ${competition.nome}`)

        for (const match of matches.slice(0, 50)) { // Limitar partidas por competição
          try {
            const existingMatch = await executeQuery(`
              SELECT id FROM jogos WHERE api_id = ?
            `, [match.id.toString()])

            if (existingMatch.length === 0) {
              const homeTeamId = await getOrCreateTeam(match.homeTeam)
              const awayTeamId = await getOrCreateTeam(match.awayTeam)

              if (homeTeamId && awayTeamId) {
                await executeQuery(`
                  INSERT INTO jogos (
                    campeonato_id, time_casa_id, time_fora_id, data_jogo,
                    rodada, status, api_id, data_criacao
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                `, [
                  competition.id,
                  homeTeamId,
                  awayTeamId,
                  match.utcDate,
                  match.matchday || 1,
                  'agendado',
                  match.id.toString()
                ])

                totalStats.matches.added++
              }
            }

          } catch (matchError) {
            console.error(`❌ Erro ao processar partida ${match.id}:`, matchError)
          }
        }

        await new Promise(resolve => setTimeout(resolve, 400)) // Rate limit

      } catch (compError) {
        console.error(`❌ Erro ao processar partidas de ${competition.nome}:`, compError)
      }
    }

    // Função auxiliar para buscar ou criar time
    async function getOrCreateTeam(teamData: any): Promise<number | null> {
      try {
        const existing = await executeQuery(`
          SELECT id FROM times WHERE api_id = ? OR nome = ?
        `, [teamData.id?.toString(), teamData.name])

        if (existing.length > 0) {
          return existing[0].id
        }

        const result = await executeQuery(`
          INSERT INTO times (
            nome, nome_curto, logo_url, api_id, data_criacao
          ) VALUES (?, ?, ?, ?, NOW())
        `, [
          teamData.name,
          teamData.shortName || teamData.name.substring(0, 15),
          teamData.crest || null,
          teamData.id?.toString()
        ])

        totalStats.teams.added++
        return result.insertId
      } catch (error) {
        console.error(`❌ Erro ao criar time ${teamData.name}:`, error)
        return null
      }
    }

    console.log("🎉 SINCRONIZAÇÃO COMPLETA FINALIZADA!")

    return NextResponse.json({
      success: true,
      message: `Sincronização completa realizada com sucesso!`,
      data: totalStats
    })

  } catch (error) {
    console.error("❌ Erro na sincronização completa:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

// Função para retornar erro quando não há token da API
async function syncMockDataExpanded() {
  console.log("❌ Token da API não configurado...")

  // Retornar erro em vez de dados mock


  return NextResponse.json({
    success: false,
    message: "Token da API Football Data não configurado. Configure FOOTBALL_API_TOKEN para sincronização real.",
    error: "API_TOKEN_MISSING"
  })
}
