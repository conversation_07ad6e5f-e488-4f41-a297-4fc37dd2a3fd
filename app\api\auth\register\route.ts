import { type NextRequest, NextResponse } from "next/server"
import { executeQuery, executeQuerySingle } from "@/lib/db"
import bcrypt from 'bcryptjs'

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {


    const body = await request.json()
    const { nome, email, telefone, cpf, senha, affiliate_code } = body

    console.log("📝 Registrando usuário:", { nome, email, telefone, cpf, affiliate_code })

    // Validações básicas
    if (!nome || !email || !senha) {
      return NextResponse.json(
        { error: "Nome, email e senha são obrigatórios" },
        { status: 400 }
      )
    }

    if (!email.includes("@")) {
      return NextResponse.json(
        { error: "Email inválido" },
        { status: 400 }
      )
    }

    if (senha.length < 6) {
      return NextResponse.json(
        { error: "Senha deve ter pelo menos 6 caracteres" },
        { status: 400 }
      )
    }

    // Verificar se email já existe
    const existingUser = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE email = ?",
      [email]
    )

    if (existingUser) {
      return NextResponse.json(
        { error: "Email já cadastrado" },
        { status: 409 }
      )
    }

    // Hash da senha
    const senhaHash = await bcrypt.hash(senha, 10)

    let afiliadoId = null

    // Se há código de afiliado, verificar se é válido
    if (affiliate_code) {
      const afiliado = await executeQuerySingle(
        "SELECT id FROM afiliados WHERE codigo_afiliado = ? AND status = 'ativo'",
        [affiliate_code]
      )

      if (afiliado) {
        afiliadoId = afiliado.id
        console.log("✅ Código de afiliado válido:", affiliate_code)
      } else {
        console.log("⚠️ Código de afiliado inválido:", affiliate_code)
      }
    }

    // Inserir usuário
    const result = await executeQuery(
      `INSERT INTO usuarios (nome, email, telefone, cpf_cnpj, senha_hash, tipo, afiliado_id, status)
       VALUES (?, ?, ?, ?, ?, 'usuario', ?, 'ativo')`,
      [nome, email, telefone, cpf, senhaHash, afiliadoId]
    )

    const userId = result.insertId

    // Se foi indicado por afiliado, processar indicação
    if (afiliadoId) {
      try {
        // Buscar dados do afiliado
        const afiliado = await executeQuerySingle(
          "SELECT * FROM afiliados WHERE id = ?",
          [afiliadoId]
        )

        if (afiliado) {
          // Calcular comissão
          let valorComissao = 0
          if (afiliado.tipo_comissao === 'cpa') {
            valorComissao = parseFloat(afiliado.cpa_valor || 0)
          } else {
            // Para percentual, usar valor base de R$ 25
            const valorBase = 25.00
            valorComissao = (valorBase * parseFloat(afiliado.percentual_comissao || 0)) / 100
          }

          // Registrar indicação
          await executeQuery(
            `INSERT INTO afiliado_indicacoes (afiliado_id, usuario_indicado_id, valor_comissao, status)
             VALUES (?, ?, ?, 'pendente')`,
            [afiliadoId, userId, valorComissao]
          )

          // Atualizar estatísticas do afiliado
          await executeQuery(
            `UPDATE afiliados SET 
              total_indicacoes = total_indicacoes + 1,
              comissao_total = comissao_total + ?,
              data_atualizacao = NOW()
             WHERE id = ?`,
            [valorComissao, afiliadoId]
          )

          console.log("✅ Indicação processada:", {
            afiliado: afiliado.nome,
            usuario: nome,
            comissao: valorComissao
          })
        }
      } catch (error) {
        console.error("❌ Erro ao processar indicação:", error)
        // Não falhar o registro se houver erro na indicação
      }
    }

    // Retornar dados do usuário (sem senha)
    const userData = {
      id: userId,
      nome,
      email,
      telefone,
      cpf,
      tipo: 'usuario',
      afiliado_id: afiliadoId
    }

    console.log("✅ Usuário registrado com sucesso:", userData)

    return NextResponse.json({
      success: true,
      message: "Usuário registrado com sucesso",
      user: userData,
      is_affiliate_referral: !!afiliadoId
    })

  } catch (error: any) {
    console.error("❌ Erro ao registrar usuário:", error)
    
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        message: "Não foi possível registrar o usuário"
      },
      { status: 500 }
    )
  }
}
