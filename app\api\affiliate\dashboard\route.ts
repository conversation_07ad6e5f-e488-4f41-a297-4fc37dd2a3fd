import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    if (!userId) {
      return NextResponse.json(
        { error: "user_id é obrigatório" },
        { status: 400 }
      )
    }

    console.log("🔍 Buscando dados do afiliado para usuário:", userId)

    // Buscar afiliado pelo usuario_id ou criar um temporário
    let afiliado = await executeQuerySingle(
      "SELECT * FROM afiliados WHERE usuario_id = ? AND status = 'ativo'",
      [userId]
    )

    if (!afiliado) {
      // Se não existe afiliado, criar dados temporários para o usuário
      console.log("⚠️ Usuário não é afiliado, criando dados temporários")

      afiliado = {
        id: parseInt(userId),
        nome: "Usuário",
        email: "<EMAIL>",
        codigo_afiliado: `AF${userId}${Date.now().toString().slice(-4)}`,
        percentual_comissao: 10,
        cpa_valor: 25.00,
        tipo_comissao: "percentual",
        comissao_total: 0,
        total_indicacoes: 0,
        status: "ativo"
      }
    }

    // Buscar indicações reais do banco (se afiliado existe)
    let indicacoes = []
    if (afiliado.id && typeof afiliado.id === 'number' && afiliado.id < 1000000) {
      indicacoes = await executeQuery(`
        SELECT
          ai.id,
          ai.valor_comissao,
          ai.status,
          ai.data_indicacao,
          u.nome as usuario_nome,
          u.email as usuario_email
        FROM afiliado_indicacoes ai
        LEFT JOIN usuarios u ON ai.usuario_indicado_id = u.id
        WHERE ai.afiliado_id = ?
        ORDER BY ai.data_indicacao DESC
      `, [afiliado.id])
    }

    // Calcular estatísticas reais
    const totalIndicacoes = indicacoes.length
    const comissaoTotal = parseFloat(afiliado.comissao_total || 0)

    const comissaoMes = indicacoes
      .filter(ind => {
        const dataIndicacao = new Date(ind.data_indicacao)
        const agora = new Date()
        return dataIndicacao.getMonth() === agora.getMonth() &&
               dataIndicacao.getFullYear() === agora.getFullYear()
      })
      .reduce((sum, ind) => sum + parseFloat(ind.valor_comissao || 0), 0)

    const indicacoesPendentes = indicacoes.filter(ind => ind.status === "pendente").length

    const stats = {
      totalIndicacoes,
      comissaoTotal,
      comissaoMes,
      indicacoesPendentes
    }

    // Preparar dados do afiliado
    const afiliadoData = {
      ...afiliado,
      link_afiliado: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/?ref=${afiliado.codigo_afiliado}`
    }

    console.log("✅ Dados reais do afiliado carregados:", {
      afiliado: afiliado.nome,
      indicacoes: indicacoes.length,
      stats
    })

    return NextResponse.json({
      success: true,
      afiliado: afiliadoData,
      indicacoes: indicacoes || [],
      stats
    })

  } catch (error: any) {
    console.error("❌ Erro ao buscar dados do afiliado:", error)
    
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        message: "Não foi possível carregar dados do afiliado"
      },
      { status: 500 }
    )
  }
}
