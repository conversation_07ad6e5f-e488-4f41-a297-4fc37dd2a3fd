// Testar API de edição de bolão

async function testBolaoEditAPI() {
  try {
    console.log('🧪 Testando API de edição de bolão...')
    
    // Primeiro, buscar um bolão existente
    const boloesResponse = await fetch('http://localhost:3000/api/boloes')
    const boloesData = await boloesResponse.json()
    
    if (!boloesData.success || !boloesData.boloes || boloesData.boloes.length === 0) {
      console.log('❌ Nenhum bolão encontrado para testar')
      return
    }
    
    const bolao = boloesData.boloes[0]
    console.log(`📋 Testando edição do bolão: ${bolao.nome} (ID: ${bolao.id})`)
    
    // Dados de teste para edição
    const dadosEdicao = {
      nome: bolao.nome + ' - Editado',
      descricao: 'Descrição editada via teste',
      valor_aposta: 30.00,
      premio_total: 15000.00,
      max_participantes: 500,
      data_inicio: new Date().toISOString(),
      data_fim: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'ativo',
      banner_image: '/uploads/banners/banner_teste.webp',
      campeonatos_selecionados: [
        { id: 1, nome: 'Campeonato Brasileiro Série A' },
        { id: 2, nome: 'Premier League' }
      ]
    }
    
    console.log('📦 Dados de edição:', {
      nome: dadosEdicao.nome,
      valor_aposta: dadosEdicao.valor_aposta,
      premio_total: dadosEdicao.premio_total,
      campeonatos: dadosEdicao.campeonatos_selecionados.length
    })
    
    // Fazer a requisição PUT
    const response = await fetch(`http://localhost:3000/api/admin/boloes/${bolao.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dadosEdicao)
    })
    
    const result = await response.json()
    
    console.log(`📡 Status da resposta: ${response.status}`)
    console.log(`📋 Resultado:`, result)
    
    if (response.ok) {
      console.log('✅ Edição funcionou!')
      
      // Verificar se as mudanças foram salvas
      const verificacaoResponse = await fetch('http://localhost:3000/api/boloes')
      const verificacaoData = await verificacaoResponse.json()
      
      if (verificacaoData.success && verificacaoData.boloes) {
        const bolaoEditado = verificacaoData.boloes.find(b => b.id === bolao.id)
        
        if (bolaoEditado) {
          console.log('🔍 Verificando mudanças:')
          console.log(`   Nome: ${bolaoEditado.nome}`)
          console.log(`   Valor: R$ ${bolaoEditado.valor_aposta}`)
          console.log(`   Prêmio: R$ ${bolaoEditado.premio_total}`)
          console.log(`   Max participantes: ${bolaoEditado.max_participantes}`)
          console.log(`   Status: ${bolaoEditado.status}`)
        }
      }
      
    } else {
      console.log('❌ Erro na edição:')
      console.log(`   Erro: ${result.error}`)
      console.log(`   Mensagem: ${result.message}`)
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error.message)
  }
}

testBolaoEditAPI()
